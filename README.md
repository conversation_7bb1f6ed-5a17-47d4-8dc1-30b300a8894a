# Build

Após o clone do repositório, rode o comando abaixo para instalar as dependências:

``` npm install ```

Para executar na sua máquina:

``` npm run serve ```

Para implantar em algum ambiente, na sua máquina precisa ter o docker instalado. Após isso, faça o login no docker hub da signa usando o usuário e senha fornecido por alguém que tenha acesso.

``` docker login -u signadesenvolvimento -p xxxxx ```

> xxx = password

Após isso, você precisa fazer o build da imagem docker para o menu. É o equivalente a gerar um pacote/compilado do código, através de um arquivo dockerfile.

Basta executar o comando abaixo:

``` docker build -t signadesenvolvimento/ecargo-menu:x.x.x . ```

Isso irá gerar uma nova imagem docker na sua máquina.

Veja mais no site da documentação do docker:

https://docs.docker.com/engine/reference/builder/

Agora você precisa fazer o upload dessa imagem para o dockerhub da signadesenvolvimento que fica no próprio repositório da docker na nuvem.
Para isso você precisa ter feito o logon no terminal.

Para o upload, basta executar o comando abaixo, substituindo o x pela sua versão:

``` docker push signadesenvolvimento/ecargo-menu:x.x.x ``` 

Finalizado o upload, agora basta você alterar o arquivo docker-compose/swarm ou kubernetes do ambiente que você deseja utilizar a sua imagem.

No caso do ambiente estar usando docker-compose, o arquivo do ambiente deve estar em /opt/nome-do-ambiente/docker-compose.yml dentro do server linux.

Caso o ambiente esteja usando docker-compose/swarm, use o comando abaixo após alterar o arquivo (xxx é o nome do ambiente no docker swarm):

``` docker stack deploy -c docker-compose.yml --with-registry-auth xxxx ``` 

No caso do kubernetes, basta mudar o deployment.


# Boilerplate

#Roadmap

- Gerar plug-in no browser para obter mais dados do usuário/máquina para auditoria
	A idéia seria armazenar esses dados no browser em cookie para recuperar os dados nos headers no servidor para dar a opção da API armazenar esses dados em pontos críticos de log como cancelamento de algum registro na base.

- Permitir o sub-menu:
	Ex.:

	Menu Operacional
	
	* CT-e
	
		* Pesquisa
		* Emissão / Cadastro
		* Monitor
	
	* MDF-e
		* Pesquisa
		* Emissão / Cadastro
		* Monitor

	Menu Gerenciamento de usuários
	
	* Usuário
	
		* Pesquisa
		* Cadastro
		* Alteração Senha
	
	* Grupo
		* Perfil
		* Cadastro
