{"name": "projectname", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "start": "vue-cli-service serve", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"apollo-boost": "^0.4.3", "apollo-cache-inmemory": "^1.6.2", "axios": "^0.18.0", "blob-util": "^2.0.2", "lodash": "^4.17.15", "vue": "^2.6.6", "vue-apollo": "^3.0.0-beta.11", "vue-i18n": "^8.14.0", "vue-router": "^3.0.1", "vue-spinner": "^1.0.3", "vuetify": "^1.5.5", "vuex": "^3.0.1", "xlsx-style": "^0.8.13"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.5.0", "@vue/cli-plugin-eslint": "^3.5.0", "@vue/cli-plugin-unit-jest": "^3.9.0", "@vue/cli-service": "^3.5.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.1", "babel-jest": "^24.8.0", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.0.0", "fibers": "^5.0.0", "graphql-tag": "^2.9.0", "jest": "^24.8.0", "sass": "^1.17.2", "sass-loader": "^7.1.0", "stylus": "^0.54.5", "stylus-loader": "^3.0.1", "vue-cli-plugin-apollo": "^0.21.0", "vue-cli-plugin-vuetify": "^0.5.0", "vue-jest": "^3.0.4", "vue-template-compiler": "^2.5.21", "vuetify-loader": "^1.0.5", "webpack-bundle-analyzer": "^3.4.1"}}