server {

  add_header X-Robots-Tag "noindex, nofollow" always;
  add_header X-Frame-Options "SAMEORIGIN" always;
  add_header X-Content-Type-Options "nosniff" always;
  add_header X-XSS-Protection "1; mode=block" always;
  add_header Content-Security-Policy "frame-src 'self'; connect-src 'self'" always;
  add_header Referrer-Policy "strict-origin-when-cross-origin" always;
  add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
  server_tokens off;

  location /Api/Login {
  	  proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_set_header X-NginX-Proxy true;
      proxy_pass     			https://ecargo.norcoast.com.br/Vue/Login/Api/Login/;
      proxy_redirect off;
      proxy_set_header Host		ecargo.norcoast.com.br;
  }

  location / {
      root /usr/share/nginx/html;
      try_files $uri $uri/ /index.html;
  }
}
