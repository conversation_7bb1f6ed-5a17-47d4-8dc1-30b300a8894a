import { shallowMount } from '@vue/test-utils'
import Chat from '@/components/common/Chat'
import Vue from 'vue'
import Vuetify from 'vuetify'

Vue.use(Vuetify)

test('Chat.vue', () => {
  const wrapper = shallowMount(Chat, {})
  setTimeout(() => {
    expect(wrapper.find('.message').text()).toEqual('O<PERSON><PERSON>, sou um Assistente Virtual programado para lhe ajudar, qual a sua dúvida?')
  }, 5000)
})
