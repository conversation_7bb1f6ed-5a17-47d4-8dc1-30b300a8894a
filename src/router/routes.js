import Login from "@/views/Login";
import SignIn from "@/components/Login/SignIn";
import ForgotPassword from "@/views/RecoveryPassword";
import SharedLink from "@/views/Dashboard/Shared/Link";
import DashboardWraper from "@/views/Dashboard/Wraper";
import DashboardInitial from "@/views/Dashboard/Initial";
import DashboardPage from "@/views/Dashboard/Page";

export default [
  {
    path: "/",
    component: Login,
    children: [
      {
        path: "/",
        name: "login_form",
        component: SignIn,
        meta: { guest: true }
      },
      {
        path: "/forgot-password",
        name: "forgot_password",
        component: ForgotPassword,
        meta: { guest: true }
      }
    ]
  },
  {
    path: "/shared/item",
    name: "share_link",
    component: SharedLink,
    meta: { requiresAuth: false }
  },
  {
    path: "/dashboard",
    component: DashboardWraper,
    meta: { requiresAuth: true },
    children: [
      {
        path: "",
        name: "dashboard",
        component: DashboardInitial,
        meta: { requiresAuth: true }
      },
      {
        path: ":father/:children",
        name: "page",
        component: DashboardPage,
        meta: { requiresAuth: true }
      }
    ]
  }
];
