import axios from 'axios'
import { DEFAULT_API_URLS } from './enum'
import { getStorage, setStorage } from './localStorage'

import store from '../store'
import { SET_API_CALL_IN_PROGRESS, SET_API_CALL_IN_ERROR } from '../store/root'

const { stringify, parse } = JSON
export const parseNetworkError = error => parse(stringify(error))

const withBaseURLContext = () => process.env.NODE_ENV
  ? DEFAULT_API_URLS[process.env.NODE_ENV.toUpperCase()]
  : DEFAULT_API_URLS.development

const HTTPClient = axios.create({
  baseURL: withBaseURLContext()
})

HTTPClient.defaults.headers.common['Content-Type'] = 'application/json'

HTTPClient.interceptors.request.use(config => {
  store.commit(SET_API_CALL_IN_PROGRESS, true)

  const token = getStorage('token')
  if (token) {
    config.headers.common.Authorization = `Bearer ${token}`
  }

  return config
}, response => Promise.reject(response))

HTTPClient.interceptors.response.use(response => {
  store.commit(SET_API_CALL_IN_PROGRESS, false)
  return response
}, error => {
  store.commit(SET_API_CALL_IN_PROGRESS, false)
  store.commit(SET_API_CALL_IN_ERROR, true)

  const originalRequest = error.config

  if (error.code !== 'ECONNABORTED' && error.response.status === 401) {
    if (!originalRequest._retry) {
      originalRequest._retry = true
      return HTTPClient
        .post('/Login/RefreshToken', {
          RefreshToken: getStorage('refresh_token'),
          UserId: getStorage('id')
        })
        .then(response => {
          setStorage('token', response.data.AccessToken)
          setStorage('refresh_token', response.data.RefreshToken)
          error.response.config.headers['Authorization'] = `Bearer ${response.data.AccessToken}`
          return HTTPClient(originalRequest)
        })
    } else {
      console.log('Fodeu nao pode continuar')
    }
  }

  return Promise.reject(error)
})

export { HTTPClient }
