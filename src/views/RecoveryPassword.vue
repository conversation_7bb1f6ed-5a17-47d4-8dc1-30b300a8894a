<template>
  <div class="container">
    <v-dialog v-model="loading" persistent fullscreen>
      <v-container>
        <v-layout>
          <v-progress-circular indeterminate :size="70" :width="7" color="secondary" />
        </v-layout>
      </v-container>
    </v-dialog>

    <v-form
      class="form-custom pt-0"
      v-if="!loading && !success"
      v-model="valid"
      lazy-validation
      ref="form-password"
    >
      <h2>{{ $t('recoveryPassword') }}</h2>

      <v-text-field
        :label="$t('email')"
        v-model="email"
        :rules="[rules.required]"
      />

      <div class="form-custom__actions">
        <router-link :to="{ name: 'login_form' }">
          <v-icon>keyboard_backspace</v-icon>
        </router-link>

        <v-btn
          :class="valid ? 'form-custom__button' : 'disabled'"
          flat
          dark
          @click="recovery"
          :disabled="!valid"
        >
          {{ $t('recovery') }}
        </v-btn>
      </div>
    </v-form>

    <v-snackbar
      v-model="snackbar"
    >
      {{ message }}
      <v-btn
        flat
        @click="snackbar = false"
      >
        Fechar
      </v-btn>
    </v-snackbar>
    <Message v-if="success" />
  </div>
</template>

<script>
import Message from '@/components/RecoveryPassword/Message'
import rules from '@/utils/rules'

export default {
  data(){
    return{
      snackbar: false,
      message: '',
      email: '',
      success: false,
      rules,
      valid: true
    }
  },
  computed: {
    loading(){
      return this.$store.getters['Account/loading']
    }
  },
  components: { Message },
  methods: {
    async recovery(){
      try{
        await this.$store.dispatch('Account/recovery', this.email)

        this.success = true
      }catch(e){
        this.message = e.split('\"')[1]
        this.snackbar = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  @import 'essencials';

  .container{
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .form-custom{
    width: 60%;
    display: flex;
    flex-direction: column;

    @include breakpoint-mobile {
      width: 90%;
    }

    h2{
      margin-bottom: 30px;
      color: #444;
    }

    &__actions{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    a{
      text-decoration: none;
    }

    &__button{
      background: #2F80ED;
      align-self: flex-end;
      display: flex;
      font-weight: 600;
    }
  }

  .disabled{
    background: rgba(0,0,0,0.12);
    color: red !important;
    font-weight: 600;
  }
</style>
