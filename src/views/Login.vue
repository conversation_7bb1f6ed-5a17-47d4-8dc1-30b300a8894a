<template>
  <v-card flat color="#fff" height="100%">
    <v-layout wrap>
      <div class="language">
        <Language />
      </div>

      <v-flex md5 v-if="breakpoint">
        <Welcome />
      </v-flex>
      <v-flex xs12 md7 class="contentLogin" style="order: 2">
        <!-- <transition name="page" mode="default"> -->
          <router-view />
        <!-- </transition> -->
      </v-flex>
    </v-layout>

    <iframe src="/asp/SessionAbandon.asp" style="height: 0; display: none;" />
  </v-card>
</template>

<script>
import Welcome from '@/components/Login/Welcome'
import SignIn from '@/components/Login/SignIn'
// import Logo from '@/components/Login/Logo'
import Language from '@/components/common/Language'

export default {
  computed: {
    breakpoint () {
      return this.$vuetify.breakpoint.width >= 960
    }
  },
  components: { Welcome, SignIn, Language }
}
</script>

<style lang="scss" scoped>
  .contentLogin{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    flex-direction: column;
  }

  .language{
    position: absolute;
    top: 0;
    right: 20px;
  }
</style>
