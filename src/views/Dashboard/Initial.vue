<template>
  <v-card flat color="transparent" height="100%">
    <v-card-text>
      <v-content>
        <Cards />
        <iframe :src="urlConnection" style="height: 0;" v-if="userAccount.id" />
      </v-content>
    </v-card-text>
  </v-card>
</template>

<script>
import Cards from '@/components/Initial/Cards'

export default {
  components: { Cards },
  computed: {
    userAccount () {
      return this.$store.state.Account.user
    },
    urlConnection () {
      return process.env.NODE_ENV === 'development' ? `https://apresentacao.ecargoasp.com.br/LoginAPI.asp?UsuarioId=${this.userAccount.id}&EmpresaId=1&Usuario=${this.userAccount.name}&PontoOperacaoId=1` : `/asp/LoginAPI.asp?UsuarioId=${this.userAccount.id}&EmpresaId=1&Usuario=${this.userAccount.name}&PontoOperacaoId=1`
    }
  }
}
</script>
