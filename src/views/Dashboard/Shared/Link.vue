<template >
  <v-card flat color="transparent" height="100%" v-if="dados">
    <v-card-text>
      <v-content>
        <h1>Pesquisa NF/CT</h1>

        <v-expansion-panel class="expensasion-ul" expand v-model="panel">
          <!-- CONHECIMENTO -->
          <v-expansion-panel-content>
            <template v-slot:header>Conhecimento</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Empresa</th>
                      <th>Emissão</th>
                      <th>TP Docto</th>
                      <th>Tipo de Pedido</th>
                      <th>Status Transporte</th>
                      <th>Status Financeiro</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>{{dados.conhecimento && dados.conhecimento.nomeEmpresa ? dados.conhecimento.nomeEmpresa : '' }}</td>
                      <td>{{dados.conhecimento && dados.conhecimento.dataEmissaoConhecimento ? dados.conhecimento.dataEmissaoConhecimento : '' }}</td>
                      <td>{{dados.conhecimento && dados.conhecimento.tipoDocumento ? dados.conhecimento.tipoDocumento : ''}}</td>
                      <td>{{dados.conhecimento && dados.conhecimento.tipoPedido ? dados.conhecimento.tipoPedido : '' }}</td>
                      <td>{{dados.conhecimento && dados.conhecimento.statusTransporte ? dados.conhecimento.statusTransporte : ''}}</td>
                      <td>{{dados.conhecimento && dados.conhecimento.statusFinanceiro ? dados.conhecimento.statusFinanceiro : '' }}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- DADOS DE ENTREGA -->
          <v-expansion-panel-content>
            <template v-slot:header>Dados de Entrega</template>

            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Previsão de Entrega</th>
                      <th>Chegada a Filial</th>
                      <th>Entrega</th>
                      <th>Recebido por</th>
                      <th>Eficiência</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>{{ dados.dadosEntrega && dados.dadosEntrega.dataPrevisaoEntrega ? dados.dadosEntrega.dataPrevisaoEntrega : ''}}</td>
                      <td>{{dados.dadosEntrega && dados.dadosEntrega.dataChegadaFilial ? dados.dadosEntrega.dataChegadaFilial : ''}}</td>
                      <td>{{dados.dadosEntrega && dados.dadosEntrega.dataEntrega ? dados.dadosEntrega.dataEntrega : '' }}</td>
                      <td>{{dados.dadosEntrega && dados.dadosEntrega.nomeRecebedor ? dados.dadosEntrega.nomeRecebedor : '' }}</td>
                      <td>{{dados.dadosEntrega && dados.dadosEntrega.eficiencia ? dados.dadosEntrega.eficiencia : ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- DADOS DOS DOCUMENTOS -->
          <v-expansion-panel-content>
            <template v-slot:header>Dados dos Documentos</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Notas Fiscais</th>
                      <th>Pedido</th>
                      <th>Filial Origem</th>
                      <th>Filial Destino</th>
                      <th>Data Emissão Manifesto</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>
                        <v-icon size="19" style="cursor:pointer;" @click="notas = true">visibility</v-icon>
                        {{ dados.dadosDocumento && dados.dadosDocumento.notasFicais > 0 ? dados.dadosDocumento.notasFicais[0] : [] }}
                      </td>
                      <td>{{dados.dadosDocumento && dados.dadosDocumento.pedido ? dados.dadosDocumento.pedido : '' }}</td>
                      <td>{{dados.dadosDocumento && dados.dadosDocumento.filialOrigem ? dados.dadosDocumento.filialOrigem : ''}}</td>
                      <td>{{dados.dadosDocumento && dados.dadosDocumento.filialDestino ? dados.dadosDocumento.filialDestino : ''}}</td>
                      <td>{{dados.dadosDocumento && dados.dadosDocumento.dataEmissaoManifesto ? dados.dadosDocumento.dataEmissaoManifesto : '' }}</td>
                    </tr>
                  </tbody>

                  <thead>
                    <tr>
                      <th>Data Emissão Vale Frete</th>
                      <th>Nº Vale Frete</th>
                      <th>N° Manifesto</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>{{dados.dadosDocumento && dados.dadosDocumento.dataEmissaoValeFrete ? dados.dadosDocumento.dataEmissaoValeFrete: '' }}</td>
                      <td>{{dados.dadosDocumento && dados.dadosDocumento.numeroValeFrete ? dados.dadosDocumento.numeroValeFrete: '' }}</td>
                      <td class="link-style">{{dados.dadosDocumento && dados.dadosDocumento.numeroManifesto ? dados.dadosDocumento.numeroManifesto: ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- OBSERVACAO DA CARTA FRETE -->
          <v-expansion-panel-content>
            <template v-slot:header>Observação da carta frete</template>
            <v-card>
              <v-card-text>{{dados.dadosDocumento && dados.dadosDocumento.observacaoCartaFrete ? dados.dadosDocumento.observacaoCartaFrete : ''}}</v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- CLIENTE -->
          <v-expansion-panel-content>
            <template v-slot:header>Cliente</template>

            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Cliente</th>
                      <th>Tipo de Cobrança</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>{{ dados.cliente && dados.cliente.nome ? dados.cliente.nome : ''}}</td>
                      <td>{{ dados.cliente && dados.cliente.tipoCobranca ? dados.cliente.tipoCobranca : ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- PARTICIPANTES -->
          <v-expansion-panel-content>
            <template v-slot:header>Participantes</template>

            <v-card>
              <v-card-text>
                <table class="table">
                  <template v-for="(item, i) in dados.participantes">
                    <thead :key="i+'a'">
                      <tr>
                        <th>{{ item && item.tipo ? item.tipo : '' }}</th>
                        <th>CNPJ</th>
                        <th>Endereço</th>
                        <th>Município</th>
                      </tr>
                    </thead>
                    <tbody :key="i+'b'">
                      <td>{{ item && item.nome ? item.nome :'' }}</td>
                      <td>{{item && item.cpfCnpj ? item.cpfCnpj : ''}}</td>
                      <td>{{item && item.endereco ? item.endereco : '' }}</td>
                      <td>{{item && item.municipioUf ? item.municipioUf : ''}}</td>
                    </tbody>
                  </template>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <v-expansion-panel-content>
            <template v-slot:header>Transporte</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Redespachador</th>
                      <th>CNPJ</th>
                      <th>Endereço</th>
                      <th>Município</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ dados.transporte && dados.transporte.redespachador ? dados.transporte.redespachador : '' }}</td>
                      <td>{{ dados.transporte && dados.transporte.cnpj ? dados.transporte.cnpj : '' }}</td>
                      <td>{{ dados.transporte && dados.transporte.endereco ? dados.transporte.endereco : '' }}</td>
                      <td>{{ dados.transporte && dados.transporte.municipio ? dados.transporte.municipio : '' }}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>
          <v-expansion-panel-content>
            <template v-slot:header>Prestador</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Prestador</th>
                      <th>Tipo</th>
                      <th>TP.Docto</th>
                      <th>Docto Prestador</th>
                      <th>Val. Docto</th>
                      <th>Nome</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr v-for="(item, i) in getPrestadores" :key="i">
                      <td>{{item && item.numeroPrestador ? item.numeroPrestador : ''}}</td>
                      <td>{{item && item.tipo ? item.tipo : ''}}</td>
                      <td>{{item && item.tipoDocumento ? item.tipoDocumento : ''}}</td>
                      <td class="link-style">{{item && item.doctoPrestador ? item.doctoPrestador : ''}}</td>
                      <td>{{item && item.valorDocto ? item.valorDocto : ''}}</td>
                      <td>{{item && item.nome ? item.nome : ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>
          <!-- TIMELINE -->
          <v-expansion-panel-content>
            <template v-slot:header>Timeline</template>
            <div class="content-timeline">
              <div class="timeline mx-auto">
                <span
                  class="item-line"
                  v-for="(item, index) in dados.trackings"
                  :key="`timeline_${item.descricao}_${index}`"
                >
                  <div
                    :class="[item.nomeIcone  === 'checked' ? 'format-icon elevation-2 green-info' : 'format-icon elevation-2 blue-info']"
                  >
                    <v-icon
                      large
                      class="style-icon"
                      dark
                      v-if="item.nomeIcone === 'assignment' || item.nomeIcone  === 'local_shipping'|| item.nomeIcone  === 'checked'"
                    >{{item.nomeIcone}}</v-icon>
                    <div class="icon-armazem" v-if="item.nomeIcone  === 'package'"></div>
                    <span class="desc-time">{{item.descricao}}</span>
                  </div>
                  <div
                    :class="[item.nomeIcone === 'package' || dados.trackings[index +1].nomeIcone === 'package'   ? 'divider-package': 'divider-time']"
                    v-if="index + 1 !== dados.trackings.length"
                  ></div>
                </span>
                <div class="timeline-bottom">
                  <div
                    :class=" [item.nomeIcone === 'checked' ? 'format-icon-bottom elevation-2 green-info mb-4' : ' format-icon-bottom elevation-2 gray-info mb-4']"
                    v-for="(item, i) in dados.trackings"
                    :key="i"
                  >
                    <v-icon
                      large
                      class="style-icon"
                      dark
                    >{{item && item.nomeIcone ? item.nomeIcone : ''}}</v-icon>
                    <div class="content-desc-status">
                      <div class="desc-status">{{item && item.descricao ? item.descricao : ''}}</div>
                      <div class="desc-date">{{item && item.dataOrdem ? item.dataOrdem : ''}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </v-expansion-panel-content>
          <!-- BASE DE CALCULO -->
          <v-expansion-panel-content>
            <template v-slot:header>Base de Cálculo</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Origem</th>
                      <th>Destino</th>
                      <th>Peso Real</th>
                      <th>Peso Cubado</th>
                      <th>Cubagem</th>
                      <th>Peso Taxado</th>
                      <th>Valor Declarado</th>
                      <th>Volumes</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.origem ? dados.baseCalculo.origem : ''}}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.destino ? dados.baseCalculo.destino : '' }}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.pesoReal ? dados.baseCalculo.pesoReal : ''}}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.pesoCubado ? dados.baseCalculo.pesoCubado : ''}}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.cubagem ? dados.baseCalculo.cubagem : ''}}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.pesoTaxado ? dados.baseCalculo.pesoTaxado : ''}}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.valorDeclarado ? dados.baseCalculo.valorDeclarado : ''}}</td>
                      <td>{{dados.baseCalculo && dados.baseCalculo.volumes ? dados.baseCalculo.volumes : ''}}</td>
                    </tr>
                  </tbody>

                  <thead>
                    <tr>
                      <th>BU</th>
                      <th>PL</th>
                      <th>BG</th>
                      <th>OM</th>
                      <th>Acordo Comercial</th>
                      <th>Trecho</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.bu ? dados.baseCalculo.bu : ''}}</td>
                      <td>{{ dados.baseCalculo && dados.baseCalculo.pl ? dados.baseCalculo.pl : ''}}</td>
                      <td>{{dados.baseCalculo && dados.baseCalculo.bg ? dados.baseCalculo.bg : ''}}</td>
                      <td>{{dados.baseCalculo && dados.baseCalculo.om ? dados.baseCalculo.om : ''}}</td>
                      <td
                        class="link-style"
                      >{{dados.baseCalculo && dados.baseCalculo.acordoComercial ? dados.baseCalculo.acordoComercial : ''}}</td>
                      <td
                        class="link-style"
                      >{{dados.baseCalculo && dados.baseCalculo.trecho ? dados.baseCalculo.trecho : ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- COMPOSICAO DO FRETE -->

          <v-expansion-panel-content>
            <template v-slot:header>Composição do Frete</template>
            <v-card>
              <div class="content-indicativos">
                <div
                  class="indicativo"
                  v-if="dados.composicaoFrete && dados.composicaoFrete.isIcmsIssDestacado"
                >
                  <v-icon color="green darken">check</v-icon>
                  <span class="text-indic">Indic.ICMS\ISS Destac.</span>
                </div>
                <div
                  class="indicativo"
                  v-if="dados.composicaoFrete && dados.composicaoFrete.isPedagioInformativo"
                >
                  <v-icon color="green darken">check</v-icon>
                  <span class="text-indic">Pedágio Informativo</span>
                </div>
                <div
                  class="indicativo"
                  v-if="dados.composicaoFrete && dados.composicaoFrete.isUsarTabelaPedido"
                >
                  <v-icon color="green darken">check</v-icon>
                  <span class="text-indic">Usar Tab.Ped.</span>
                </div>
                <div
                  class="indicativo"
                  v-if="dados.composicaoFrete && dados.composicaoFrete.isCalculoIcmsParcelas"
                >
                  <v-icon color="green darken">check</v-icon>
                  <span class="text-indic">Cálc.ICMS nas Parcelas</span>
                </div>
                <div
                  class="indicativo"
                  v-if="dados.composicaoFrete && dados.composicaoFrete.isCalculoIssParcelas"
                >
                  <v-icon color="green darken">check</v-icon>
                  <span class="text-indic">Cálc.ISS nas Parcelas</span>
                </div>
                <div
                  class="indicativo"
                  v-if="dados.composicaoFrete && dados.composicaoFrete.isIsentoTributos"
                >
                  <v-icon color="green darken">check</v-icon>
                  <span class="text-indic">Isento Tributos</span>
                </div>
              </div>

              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Carga</th>
                      <th>Ped</th>
                      <th>Escolta</th>
                      <th>Deslocamento</th>
                      <th>SEC/CAT</th>
                      <th>PERNOITE</th>
                      <th>Despacho</th>
                      <th>Ajudante</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>{{ dados.composicaoFrete && dados.composicaoFrete.valorCarga ? dados.composicaoFrete.valorCarga : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorPedagio ? dados.composicaoFrete.valorPedagio : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorEscolta ? dados.composicaoFrete.valorEscolta : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorDeslocamento ? dados.composicaoFrete.valorDeslocamento : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorSecCat ? dados.composicaoFrete.valorSecCat : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorPernoite ? dados.composicaoFrete.valorPernoite : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorDespacho ? dados.composicaoFrete.valorDespacho : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorAjudante ? dados.composicaoFrete.valorAjudante : 0}}</td>
                    </tr>
                  </tbody>
                  <thead>
                    <tr>
                      <th>Acréscimo</th>
                      <th>Desconto</th>
                      <th>Frete Peso</th>
                      <th>Frete Valor</th>
                      <th>Custo Extra</th>
                      <th>Outros</th>
                      <th>Sub-Total</th>
                      <th>Valor Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorAcrescimo ? dados.composicaoFrete.valorAcrescimo : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorDesconto ? dados.composicaoFrete.valorDesconto : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorFretePeso ? dados.composicaoFrete.valorFretePeso : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorFreteValor ? dados.composicaoFrete.valorFreteValor : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorCustoExtra ? dados.composicaoFrete.valorCustoExtra : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorOutros ? dados.composicaoFrete.valorOutros : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorSubTotal ? dados.composicaoFrete.valorSubTotal : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.valorTotal ? dados.composicaoFrete.valorTotal : 0 }}</td>
                    </tr>
                  </tbody>
                  <thead>
                    <tr>
                      <th>Base Cálc ICMS</th>
                      <th>ICMS</th>
                      <th>Base Cálc ISS</th>
                      <th>ISS</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.baseCalculoIcms ? dados.composicaoFrete.baseCalculoIcms : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.aliquotaIcms ? dados.composicaoFrete.aliquotaIcms : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.baseCalculoIss ? dados.composicaoFrete.baseCalculoIss : 0}}</td>
                      <td>{{dados.composicaoFrete && dados.composicaoFrete.aliquotaIss ? dados.composicaoFrete.aliquotaIss : 0}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>

          <!-- LIBERACAO DO PAGAMENTO -->
          <v-expansion-panel-content>
            <template v-slot:header>Liberação Pagamento</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Status Lib CT-e</th>
                      <th>Valor lib CT-e</th>
                      <th>Data lib CT-e</th>
                      <th>Usuario lib CT-e</th>
                      <th>Obs.Lib lib CT-e</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.statusLiberacaoCte ? dados.liberacaoPagamento.statusLiberacaoCte : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.valorLiberacaoCte ? dados.liberacaoPagamento.valorLiberacaoCte : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.dataLiberacaoCte ? dados.liberacaoPagamento.dataLiberacaoCte : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.usuarioLiberacaoCte ? dados.liberacaoPagamento.usuarioLiberacaoCte : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.observacaoLiberacaoCte ? dados.liberacaoPagamento.observacaoLiberacaoCte : '' }}</td>
                    </tr>
                  </tbody>

                  <thead>
                    <tr>
                      <th>Status Lib Fatura</th>
                      <th>Valor lib Fatura</th>
                      <th>Data lib Fatura</th>
                      <th>Usuario lib Fatura</th>
                      <th>Obs.Lib lib Fatura</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.statusLiberacaoFatura ? dados.liberacaoPagamento.statusLiberacaoFatura : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.valorLiberacaoFatura ? dados.liberacaoPagamento.valorLiberacaoFatura : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.dataLiberacaoFatura ? dados.liberacaoPagamento.dataLiberacaoFatura : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.usuarioLiberacaoFatura ? dados.liberacaoPagamento.usuarioLiberacaoFatura : '' }}</td>
                      <td>{{ dados.liberacaoPagamento && dados.liberacaoPagamento.observacaoLiberacaoFatura ? dados.liberacaoPagamento.observacaoLiberacaoFatura : '' }}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>
          <!-- DADOS DA FATURA -->
          <v-expansion-panel-content>
            <template v-slot:header>Dados da Fatura</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>N° Pré fatura</th>
                      <th>Val. Pré fatura</th>
                      <th>Emissão</th>
                      <th>Nº Fatura</th>
                      <th>Val.Fatura</th>
                      <th>Vencimento</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr>
                      <td
                        class="link-style"
                      >{{dados.dadosFatura && dados.dadosFatura.numeroPreFatura ? dados.dadosFatura.numeroPreFatura : '' }}</td>
                      <td>{{dados.dadosFatura && dados.dadosFatura.valorPreFatura ? dados.dadosFatura.valorPreFatura : ''}}</td>
                      <td>{{dados.dadosFatura && dados.dadosFatura.emissao ? dados.dadosFatura.emissao : ''}}</td>
                      <td>{{dados.dadosFatura && dados.dadosFatura.numeroFatura ? dados.dadosFatura.numeroFatura : ''}}</td>
                      <td>{{dados.dadosFatura && dados.dadosFatura.valorFatura ? dados.dadosFatura.valorFatura : ''}}</td>
                      <td>{{dados.dadosFatura && dados.dadosFatura.vencimento ? dados.dadosFatura.vencimento : ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>
          <v-expansion-panel-content>
            <template v-slot:header>Ocorrências Financeiras</template>
            <v-card>
              <v-card-text>
                <table class="table">
                  <thead>
                    <tr>
                      <th>Data</th>
                      <th>Ocorrência</th>
                    </tr>
                  </thead>

                  <tbody>
                    <tr v-for="(item, i) in dados.ocorrenciasFinanceiras" :key="i">
                      <td
                        style="border-bottom: 1px solid #ccc;"
                      >{{item && item.data ? item.data: '' }}</td>
                      <td
                        style="border-bottom: 1px solid #ccc;"
                      >{{item && item.ocorrencia ? item.ocorrencia: ''}}</td>
                    </tr>
                  </tbody>
                </table>
              </v-card-text>
            </v-card>
          </v-expansion-panel-content>
          <!-- OCORRENCIAS FINANCEIRAS -->
        </v-expansion-panel>
      </v-content>
    </v-card-text>

    <v-dialog v-model="notas" scrollable max-width="230">
      <v-card>
        <v-card-title class="headline">Nota fiscal/série</v-card-title>
        <v-divider />
        <v-card-text style="max-height: 300px;">
          <ul style="list-style: none;">
            <li
              style="color: #666; margin-bottom: 10px;"
              v-for="(nota, i) in getNotasFiscais"
              :key="i"
            >{{ nota }}</li>
          </ul>
        </v-card-text>
        <v-divider />
        <v-card-actions @click="notas = false">
          <v-btn>Fechar</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script>
import axios from "axios";
export default {
  data() {
    return {
      dados: {},
      //dados2: null,
      notas: false,
      panel: [
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        true
      ],
    }
  },
  computed: {
    getPrestadores() {
      if (this.dados.prestadores && this.dados.prestadores.numeroPrestador) {
        const filtered = this.dados.prestadores.numeroPrestador.filter(item => item !== null)
        return filtered
      }
    },
    getNotasFiscais() {
      return this.dados.dadosDocumento && this.dados.dadosDocumento.notasFicais
        ? this.dados.dadosDocumento.notasFicais
        : []
    },
  },
  mounted() {
    this.handleData()
  },
  methods: {
    async handleData() {
      try {
        const { data } = await axios.get("http://***********/Desenv/Oliveira/Signa.PesquisaNfCt.Api/nfct/5642")//5642 5623
        this.dados = data
        console.log('data', data)
      } catch (e) {
        console.log(e)
      }
    }
  }
};
</script>

<style scoped lang="scss">
.timeline-bottom {
  width: 80%;
  margin: 0 auto;
  margin-top: 80px;
}
timeline-bottom .content-item {
  margin: 0 auto;
}
.content-timeline .content-status-transport {
  margin: 0 auto;
  width: 300px;
}
.desc-status {
  font-weight: 600;
  color: black;
}
.desc-date {
  color: #666;
}
.content-desc-status {
  margin-top: -43px;
  transform: translateX(55px);
  min-width: 200px;
  padding-left: 5px;
  text-align: left;
}
.status-transporte {
  padding-left: 20px;
  margin: 50px auto;
  text-align: left;
  width: 50%;
  margin-bottom: 10px;
}
.icon-armazem {
  width: 50px;
  height: 50px;
  background-image: url("../../../assets/armazem.png");
  background-repeat: no-repeat;
  background-size: 60%;
  background-position: 20% 5%;
  transform: translateY(5px);
}
.icon-caminhao {
  background-image: url("../../../assets/caminhao.png");
  background-repeat: no-repeat;
  background-size: 90%;
  background-position: 50% 60%;
}
.divider-package,
.divider-time {
  border-bottom: solid 2px rgb(185, 185, 185);
  width: 170px;
  display: inline-block;
}
.divider-time {
  transform: translateY(-10px);
}
.divider-package {
  transform: translateY(-20px);
}
.desc-time {
  position: absolute;
  bottom: -30px;
  left: 0;
  transform: translate(-45px, 15px);
  width: 150px;
  font-weight: 600;
  color: #666;
  padding-top: 20px;
}
.content-timeline {
  min-height: 400px;
  .timeline {
    height: auto;
    padding: 16px;
    text-align: center;
    // min-width: 700px;
  }
}
.format-icon {
  position: relative;
  display: inline-block;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  padding: 5px;
}
.format-icon-bottom {
  margin-bottom: 16px;
  position: relative;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  padding: 5px;
  display: inline-block;
}
.format-icon-bottom:not(:first-of-type) {
  margin-left: 220px;
}
.blue-info {
  background-color: #3499db;
}
.gray-info {
  background-color: #b6b8bc;
}
.green-info {
  background-color: #00b894;
}
.style-icon {
  padding-top: 2px;
  //  transform: translateX(6%);
}
.content-indicativos {
  background-color: #f5f8fd;
  padding-bottom: 16px;
  display: flex;
  flex-wrap: wrap;
  .indicativo {
    flex: 1;
  }
}
.expensasion-ul {
  box-shadow: none;

  li {
    background-color: transparent !important;
  }
}

.table {
  width: 100%;
  thead {
    th {
      color: #3498db;
    }
  }

  tbody {
    td {
      color: #666;
    }
  }
  .link-style {
    text-decoration: underline;
    cursor: pointer;
  }
  td {
    height: 14px;
    width: 14px;
    text-align: left;
  }
}
</style>
