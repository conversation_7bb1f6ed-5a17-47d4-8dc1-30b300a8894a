<template>
  <v-app style="background: #F5F8FD;">
    <v-card flat color="transparent" height="100%">
      <v-card-text style="height: 100%">
        <v-content style="height: 100%">
          <iframe :src="url" height="300" @change="load" @load="load" ref="frame" id="frame" name="frame" :style="{ display: disconnection ? 'none' : 'inherit' }" />
        </v-content>
      </v-card-text>
    </v-card>
    <Overlay v-if="disconnection">
      <v-flex xl4 lg4 md6 sm8 xs11>
        <v-card>
          <v-card-text style="text-align: center">
            <img
              src="@/assets/disconnection.svg"
              style="width: 75%; max-width: 300px;"
            />
            <h2 style="margin-top: 20px;">Desconexão</h2>
            <p>Conexão encerrada. Pressione o botão abaixo para efetuar novo logon.</p>
            <v-btn flat dark class="button" v-on:click.native="logout">Login</v-btn>
          </v-card-text>
        </v-card>
      </v-flex>
    </Overlay>
  </v-app>
</template>

<script>
import Overview from '@/components/Analytics/Overview'
import Overlay from '@/components/common/Overlay'
import Loading from '@/components/common/Loading'

export default {
  data() {
    return {
      disconnection: false
    };
  },
  components: { Overview, Overlay, Loading },
  computed: {
    page (){
      return this.$store.state.Users.page
    },
    url (){
      return process.env.NODE_ENV === 'development' ? `https://apresentacao.ecargoasp.com.br/asp/${this.page}` : `/asp/${this.page}`
    },
    user (){
      return this.$store.state.Users.Usuario
    },
    language (){
      return this.$store.state.Account.language
    }
  },
  created (){
    window.addEventListener('beforeunload', this.logout)
  },
  watch: {
    language() {
      const win = this.$refs.frame.contentWindow;

      win.postMessage(this.language, "*");
    }
  },
  methods: {
    load (){
      document.querySelector('iframe#frame').style.visibility = 'hidden'
      try {
        if(this.$refs.frame.contentWindow.location.href.search(/menupr/i) !== -1){
          if(this.$refs.frame.contentWindow.document.getElementById('frameInicial').src.search(/desconexao/i) !== -1){
            this.disconnection = true
          }else{
            //TODO: Capturar mensagem de erro se houver (Alterar menupr para enviar a mensagem no localstorage?) Ocultar a tela com overlay.
            this.$router.push({ name: 'dashboard' })
          }
        }else if(this.$refs.frame.contentWindow.location.href.search(/desconexao/i) !== -1){
          this.disconnection = true
        }
      } catch (error) {
      }
      
      try {
        document.querySelector('iframe#frame').style.visibility = 'visible'
      } catch (error) {}
    },
    logout () {
      try {
        this.$store.commit('setUser', null)
        this.$store.commit('setPage', null)
        this.$store.commit('setLanguage', null)
        this.$store.commit('setLoading', false)

        this.$router.push({ name: 'login_form' })
      } catch (error) {
      }
    }
  }
};
</script>

<style scoped>
.button {
  background: #2f80ed;
  font-weight: 600;
}
</style>
