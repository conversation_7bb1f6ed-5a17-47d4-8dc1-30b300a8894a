<template>
  <v-card flat color="#fff" height="100%">
    <Header />
    <Sidebar />
    <v-layout wrap>
      <v-flex xs12 sm12>
        <transition name="page" mode="out-in">
          <v-app style="background: #F5F8FD;">
            <router-view />
          </v-app>
        </transition>
        <Profile />
        <ChangePassword />
        <Chat v-if="resChat" />
      </v-flex>
    </v-layout>
  </v-card>
</template>

<script>
import Header from '@/components/common/Header'
import Sidebar from '@/components/common/Sidebar'
import Profile from '@/components/Profile/Profile'
import ChangePassword from '@/components/common/ChangePassword'
import Chat from '@/components/common/Chat'
import { getStorage } from '@/utils/localStorage'

export default {
  components: { Sidebar, Header, Profile, ChangePassword, Chat },
  computed: {
    resChat(){
      return this.$store.state.Users.chat
    }
  },
  mounted(){
    this.handleWraper()

  },
  methods: {
    handleWraper(){
      this.$store.dispatch('Account/getUser')
      this.$store.dispatch('Account/getNotifications')
      this.$store.dispatch('Menu/getList')
      this.$store.dispatch('Menu/getFavorites')
      this.$store.dispatch('Users/mountPage', getStorage('currentPage'))
    }
  }
}
</script>
