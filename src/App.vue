<template>
  <v-app style="background: #F5F8FD;">
    <transition name="page" mode="out-in">
      <router-view />
    </transition>
    <Loading />
    <v-snackbar
      :value="snack"
      :timeout="mouseOver ? 0 : 10000"
      @mouseenter="mouseOver = true"
      @mouseleave="mouseOver = false"
    >
      {{ msg }}
      <v-btn
        color="blue"
        flat
        @click="$store.commit('setError', false)"
      >
        Fechar
      </v-btn>
    </v-snackbar>
  </v-app>
</template>

<script>
import Loading from '@/components/common/Loading'

export default {
  components: { Loading },
  data() {
    return {
      mouseOver: false
    }
  },
  computed: {
    snack(){
      return this.$store.state.error
    },
    msg(){
      return this.$store.state.errorMsg
    }
  },
  mounted(){
    this.$i18n.locale = localStorage.language
  }
}
</script>

<style>
  *, *::before, *::after{
    font-family: 'Open Sans', sans-serif;
  }

  .page-enter-active, .page-leave-active {
    transition: opacity .3s ease-out, transform .3s ease-out;
  }
  .page-enter, .page-leave-to /* .list-leave-active below version 2.1.8 */ {
    opacity: 0;
  }

  iframe{
    border: 0;
    width: 100%;
    height: 100%;
  }

  .v-messages{
    min-height: 14px;
  }
</style>
