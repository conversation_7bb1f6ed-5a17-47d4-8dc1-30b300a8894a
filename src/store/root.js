import { parseNetworkError } from '../utils/request'
import * as apiMethods from '../services'

import { defaultClient as apolloClient } from '@/main'

export const SET_API_CALL_IN_PROGRESS = 'SET_API_CALL_IN_PROGRESS'
export const SET_GENERAL_ERRORS = 'SET_GENERAL_ERRORS'
export const SET_API_CALL_IN_ERROR = 'SET_GENERAL_ERRORS'
export const SET_GENERAL_MSG_ERROR = 'SET_GENERAL_MSG_ERROR'

export default {
  state: {
    error: false,
    errorMsg: '',
    isAPICallInProgress: false,
    loading: false,
    generalErrors: []
  },
  mutations: {
    setLoading: (state, payload) => {
      state.loading = payload
    },
    [SET_API_CALL_IN_PROGRESS]: (state, status) => {
      state.isAPICallInProgress = status
    },
    [SET_GENERAL_ERRORS]: (state, error) => {
      state.generalErrors.push(error)
    },
    [SET_API_CALL_IN_ERROR]: (state, status) => {
      state.error = status
    },
    [SET_GENERAL_MSG_ERROR]: (state, msg) => {
      state.errorMsg = msg
    }
  },
  actions: {
    async apiMethods ({ commit }, { entity, action, payload = {}, query, params }) {
      try {
        const response = await apiMethods[entity][action]({ ...payload, query, params })
        return response
      } catch (error) {
        const errorPayload = { [`${entity}_${action}_request`]: parseNetworkError(error) }
        commit(SET_GENERAL_MSG_ERROR, error.response.data.Message)
        commit(SET_GENERAL_ERRORS, errorPayload)

        return error
      }
    },
    getAllQuerys: async ({ commit }, { query, variables }) => {
      try {
        const response = await apolloClient.query({
          query,
          variables
        })

        return response
      } catch (e) {
        return e
      }
    }
  },
  getters: {
    loading: state => state.loading
  }
}
