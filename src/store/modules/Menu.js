import { gql } from 'apollo-boost'
import { getStorage } from '@/utils/localStorage'
import { defaultClient as apolloClient } from '@/main'

export default {
  namespaced: true,
  state: {
    list: [],
    favorites: [],
    headerText: ''
  },
  mutations: {
    setList: (state, payload) => {
      state.list = payload
    },
    setFavorites: (state, payload) => {
      state.favorites = payload
    },
    setHeaderText: (state, payload) => {
      state.headerText = payload
    }
  },
  actions: {
    getList: async ({ commit }) => {
      commit('setLoading', true, { root: true })
      const { data } = await apolloClient
        .query({
          query: gql`
            query($id: ID!, $token: String){
              getMenuByUserId(id: $id, token: $token){
                id
                name
                shortName
                icon
                children {
                  id
                  shortName
                  href
                  fatherString
                  meString
                }
              }
            }
          `,
          fetchPolicy: 'no-cache',
          variables: { id: getStorage('id'), token: getStorage('token') }
        })
      commit('setList', data.getMenuByUserId)
      commit('setLoading', false, { root: true })
    },
    searchList: async ({ commit }, name) => {
      const { data } = await apolloClient
        .query({
          query: gql`
            query($name: String, $token: String){
              getFunctionByName(name: $name, token: $token) {
                id
                name
                shortName
              }
            }
          `,
          variables: { name, token: getStorage('token') }
        })
      commit('setList', data.getFunctionByName)
    },
    getFavorites: async ({ commit }) => {
      const { data } = await apolloClient
        .query({
          query: gql`
            query($id: ID!, $token: String){
              getFavoriteMenusByUserId(id: $id, token: $token){
                id
                shortName
                href
              }
            }
          `,
          fetchPolicy: 'no-cache',
          variables: { id: getStorage('id'), token: getStorage('token') }
        })
      commit('setFavorites', data.getFavoriteMenusByUserId)
    },
    getHeaderText: async ({ commit }) => {
      const { data } = await apolloClient
        .query({
          query: gql`
            query{
              getHeaderTextForMenu
            }
          `,
          fetchPolicy: 'no-cache'
        })
      commit('setHeaderText', data.getHeaderTextForMenu)
    }
  },
  getters: {
    list: state => state.list,
    favorites: state => state.favorites,
    headerText: state => state.headerText
  }
}
