import { gql } from 'apollo-boost'
import { getStorage, setStorage } from '@/utils/localStorage'
import { defaultClient as apolloClient } from '@/main'

export default {
  namespaced: true,
  state: {
    user: {},
    notification: [],
    language: getStorage.language
  },
  mutations: {
    setUser: (state, payload) => {
      state.user = { ...state.user, ...payload }
    },
    setChangePwd: (state, payload) => {
      state.user = { ...state, ...payload, changePwd: payload }
    },
    setPwdExpired: (state, payload) => {
      state.user = { ...state, ...payload, isPwdExpired: payload }
    },
    setNotification: (state, payload) => {
      state.notification = payload
    },
    setLanguage: (state, payload) => {
      state.language = payload
    }
  },
  actions: {
    login: async ({ commit }, { usuario, senha }) => {
      commit('setLoading', true, { root: true })
      try {
        const { data } = await apolloClient
          .query({
            query: gql`
              query($usuario: String, $senha: String) {
                login(usuario: $usuario, senha: $senha) {
                  accessToken
                  refreshToken
                  logo
                  userId
                }
              }
            `,
            variables: { usuario, senha },
            error (error) {
              return error
            }
          })

        setStorage('token', data.login.accessToken)
        setStorage('refresh_token', data.login.refreshToken)
        setStorage('logo', data.login.logo)
        setStorage('id', data.login.userId)
        commit('Users/SET_GENERAL_LOGIN', data.login, { root: true })
        commit('setLoading', false, { root: true })
        return data
      } catch (e) {
        commit('setLoading', false, { root: true })
        throw e.message
      }
    },
    getUser: async ({ commit }) => {
      try {
        const { data } = await apolloClient
          .query({
            query: gql`
              query($id: ID!, $token: String){
                getUser(id: $id, token: $token){
                  id
                  name
                  email
                  picture
                  changePwd
                  isPwdExpired
                }
              }
            `,
            fetchPolicy: 'no-cache',
            variables: { id: getStorage('id'), token: getStorage('token') }
          })
        commit('setUser', data.getUser)
      } catch (e) {
        return e
      }
    },
    recovery: async ({ commit }, email) => {
      commit('setLoading', true, { root: true })
      try {
        const res = await apolloClient
          .query({
            query: gql`
              query($email: String){
                recovery(email: $email){
                  message
                }
              }
            `,
            variables: { email },
            error (error) {
              return error
            }
          })
        commit('setLoading', false, { root: true })
        return res
      } catch (e) {
        commit('setLoading', false, { root: true })
        throw e.message
      }
    },
    recoveryChange: async ({ commit }, senha) => {
      commit('setLoading', true, { root: true })
      try {
        const res = await apolloClient
          .mutate({
            mutation: gql`
              mutation recoveryChange($id: ID!, $senha: String, $token: String){
                recoveryChange(id: $id, senha: $senha, token: $token){
                  message
                }
              }
            `,
            variables: { id: getStorage('id'), senha, token: getStorage('token') }
          })
        commit('setLoading', false, { root: true })
        commit('setChangePwd', false)
        commit('setPwdExpired', false)
        return res
      } catch (e) {
        commit('setLoading', false, { root: true })
        throw e.message
      }
    },
    changeAccount: async ({ commit }, input) => {
      commit('setLoading', true, { root: true })
      try {
        const { data } = await apolloClient
          .mutate({
            mutation: gql`
              mutation updateUser($id: ID!, $input: UserInput!, $token: String){
                updateUser(id: $id, input: $input, token: $token){
                  name
                }
              }
            `,
            variables: { id: getStorage('id'), input, token: getStorage('token') }
          })
        commit('setLoading', false, { root: true })
        commit('setUser', data.updateUser)
      } catch (e) {
        commit('setLoading', false, { root: true })
        throw e.message
      }
    },
    getNotifications: async ({ commit }) => {
      try {
        const { data } = await apolloClient
          .query({
            query: gql`
              query($id: ID!, $token: String){
                notification(id: $id, token: $token){
                  message
                }
              }
            `,
            variables: { id: getStorage('id'), token: getStorage('token') }
          })
        commit('setNotification', data.notification)
      } catch (e) {
        return e
      }
    },
    language: async ({ commit }, language) => {
      commit('setLanguage', language)
    }
  },
  getters: {
    user: state => state.user,
    notification: state => state.notification,
    language: state => state.language
  }
}
