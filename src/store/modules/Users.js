import { setStorage } from '@/utils/localStorage'
export const SET_GENERAL_MODAL = 'SET_GENERAL_MODAL'
export const SET_GENERAL_LOGIN = 'SET_GENERAL_LOGIN'
export const SET_GENERAL_MENU = 'SET_GENERAL_MENU'
export const SET_GENERAL_DRAWER = 'SET_GENERAL_DRAWER'
export const SET_GENERAL_LOGOUT = 'SET_GENERAL_LOGOUT'
export const SET_GENERAL_PAGE = 'SET_GENERAL_PAGE'
export const SET_GENERAL_CHAT = 'SET_GENERAL_CHAT'

export default {
  namespaced: true,
  state: {
    profile: false,
    drawer: true,
    chat: false,
    page: "",
    Auth: {},
    Notificacoes: {},
    Favoritos: [],
    Usuario: {
      IsSenhaTemporaria: false,
    },
    Menu: {},
  },
  actions: {
    changeStatusModal({ commit }, { name }) {
      commit(SET_GENERAL_MODAL, name);
    },
    login({ commit }, { payload }) {
      commit(SET_GENERAL_LOGIN, payload);
    },
    menu({ commit }, { payload }) {
      commit(SET_GENERAL_MENU, payload);
    },
    changeDrawer({ commit }, payload) {
      commit(SET_GENERAL_DRAWER, payload);
    },
    logout({ commit }) {
      commit(SET_GENERAL_LOGOUT);
    },
    mountPage({ commit }, payload) {
      commit(SET_GENERAL_PAGE, payload);
    },
    chat({ commit }) {
      commit(SET_GENERAL_CHAT);
    },
  },
  mutations: {
    [SET_GENERAL_MODAL]: (state, name) => {
      state[name] = !state[name];
    },
    [SET_GENERAL_LOGIN]: (state, payload) => {
      state.Usuario = { ...payload };
    },
    [SET_GENERAL_MENU]: (state, payload) => {
      state.Menu = payload;
    },
    [SET_GENERAL_DRAWER]: (state, payload) => {
      state.drawer = payload;
    },
    [SET_GENERAL_LOGOUT]: (state) => {
      state.Usuario = { IsSenhaTemporaria: false };
      state.Menu = {};
      window.localStorage.clear();
    },
    [SET_GENERAL_PAGE]: (state, payload) => {
      setStorage("currentPage", payload);
      state.page = payload;
    },
    [SET_GENERAL_CHAT]: (state) => {
      state.chat = !state.chat;
    },
  },
  getters: {},
};
