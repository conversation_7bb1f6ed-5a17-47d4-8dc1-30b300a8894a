<template>
  <v-container fluid grid-list-lg>
    <h1>{{ $t('favorites') }}</h1>
    <p>{{ $t('fastAccess') }}</p>

    <div class="empty" v-if="favorites.length === 0">
      <img src="@/assets/empty.svg">
      <h2>Você não possui nenhuma função nos favoritos ainda.</h2>
    </div>

    <v-layout wrap class="containerCard" v-if="favorites.length > 0">
      <v-flex
        xs12
        sm6
        md4
        lg3
        v-for="item in favorites"
        :key="item.id"
        @click="mountItem(item.href, item.id)"
      >
        <Card style="cursor: pointer;">
          <v-card-title>
            <div class="containerList">
              <div class="containerList__icon">
                <v-icon :size="17" color="#fff">star</v-icon>
              </div>
              <div class="containerList__info">
                <h2>{{ item.shortName }}</h2>
                <!-- <p>Comercial</p> -->
              </div>
            </div>
          </v-card-title>
        </Card>
      </v-flex>
    </v-layout>
  </v-container>
</template>

<script>
import Card from './Card'
import { mapGetters } from 'vuex'

export default {
  components: { Card },
  computed: {
    ...mapGetters({ favorites: 'Menu/favorites' }),
    list(){
      return this.$store.state.Users.Usuario.favoriteMenus
    }
  },
  methods: {
    mountItem(item, query){
      this.$router.push({ name: 'page', params: { query } })
      this.$store.dispatch('Users/mountPage', item)
    }
  }
}
</script>

<style lang="scss" scoped>
  h1{
    color: #3498DB;
  }

  .empty{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 150px;

    img{
      max-width: 300px;
      margin-bottom: 20px;
    }

    h2{
      font-weight: 400;
      color: #444;
      font-size: 16px;
    }
  }

  .containerCard{
    margin-top: 35px !important;
  }

  .containerList{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    &__icon{
      width: 34px;
      height: 34px;
      border-radius: 50%;
      background: #3498DB;
      justify-content: center;
      align-items: center;
      display: flex;
    }

    &__info{
      margin-left: 10px;
      h2{
        font-size: 16px;
        color: #888;
        font-weight: 600;
      }

      p{
        font-size: 12px;
        color: #ABA8A8;
        font-weight: 600;
        margin-bottom: 0;
      }
    }

    button{
      width: 60%;
      height: 40px;
      margin-top: 20px;
      border-radius: 3px;
      background: #56CCF2;
      color: #fff;
      font-weight: 600;
      background: -webkit-linear-gradient(-50deg, #2F80ED, #56CCF2);
      background: linear-gradient(-50deg, #2F80ED, #56CCF2);
    }
  }
</style>
