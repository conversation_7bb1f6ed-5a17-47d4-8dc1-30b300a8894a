<template>
  <v-container grid-list-lg>
    <v-layout wrap>
      <v-flex xs12 sm12>
        <h2>Analytics</h2>
      </v-flex>

      <v-flex
        v-for="item in analyticsOverview"
        :key="item.icon"
        sm3
      >
        <Card class="card">
          <v-card-title primary-title>
            <div>
              <v-icon
                class="icon"
                :color="item.icon.color"
                :style="{'background': item.icon.box }"
                :size="17"
              >
                {{ item.icon.name }}
              </v-icon>
              <h3>{{ item.value }}</h3>
              <p class="card__desc">{{ item.desc }}</p>

              <p
                v-if="item.analytics"
                class="card__analytics"
                :style="{'color': item.analytics.up ? '#2ecc71' : '#e74c3c'}"
              >
                <v-icon
                  :color="item.analytics.up ? '#2ecc71' : '#e74c3c'"
                >
                  {{ item.analytics.icon }}
                </v-icon>
                {{ item.analytics.value }}
              </p>
            </div>
          </v-card-title>
        </Card>
      </v-flex>
    </v-layout>
  </v-container>
</template>

<script>
  import Card from '@/components/common/Card'

  export default {
    data(){
      return{
        analyticsOverview: [
          { icon: { name: 'person', color: '#0984e3', box: '#a4cff9' }, value: '21.2k', desc: 'Total de usuarios' },
          { icon: { name: 'fingerprint', color: '#6c5ce7', box: '#b1acf9' }, value: '1.6k', desc: 'Impressoes', analytics: { icon: 'arrow_drop_up', up: true, value: '112.71%' } },
          { icon: { name: 'flash_on', color: '#f39c12', box: '#f7e59e' }, value: '826', desc: 'Alcance', analytics: { icon: 'arrow_drop_down', up: false, value: '24.2%' } },
          { icon: { name: 'show_chart', color: '#27ae60', box: '#9cf4c2' }, value: '18.2%', desc: 'Engajamento', analytics: { icon: 'arrow_drop_up', up: true, value: '112.71%' } },
        ]
      }
    },
    components: { Card }
  }
</script>

<style lang="scss" scoped>
  h2{
    font-size: 1.9em;
    color: #333;
    font-weight: 500;
  }

  .card{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;

    div{
      display: flex;
      flex-direction: column;
      align-items: center;

      .icon{
        width: 30px;
        height: 30px;
        border-radius: 50%;
      }

      h3{
        text-align: center;
        font-size: 3em;
      }
    }

    &__desc{
      text-align: center;
      color: #999;
      font-weight: 500;
      font-size: 12px;
    }

    &__analytics{
      display: flex;
      align-items: center;
      font-size: 12px;
    }
  }
</style>
