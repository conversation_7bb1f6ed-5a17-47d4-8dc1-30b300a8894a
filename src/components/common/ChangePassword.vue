<template>
  <v-dialog
    persistent
    v-model="user.changePwd"
    max-width="500"
  >
    <v-card class="card-custom">
      <v-card-text>
        <h2><PERSON><PERSON><PERSON> senha</h2>
        <p v-if="user.isPwdExpired">A sua senha expirou!<br>Insira uma nova senha por segurança</p>
        <p v-else>Para sua segurança é recomendavel que você troque sua senha apos a recuperação.</p>
        <v-flex sm12 class="card-custom__form">
          <v-form>
            <v-text-field
              ref="password"
              label="Nova Senha"
              placeholder=""
              type="password"
              v-model="password"
              required
            />

            <v-text-field
              ref="c-password"
              label="Confirmar Senha"
              type="password"
              placeholder=""
              v-model="cPassword"
              required
            />
          </v-form>

          <v-card-actions class="card-custom__actions">
            <v-spacer></v-spacer>
            <v-btn
              color="#54a0ff"
              dark
              class="card-custom__actions__button"
              @click="changePassword"
            >
              Salvar
            </v-btn>
          </v-card-actions>
        </v-flex>
      </v-card-text>
    </v-card>
    <v-snackbar
      v-model="snackbar"
    >
      {{ message }}
    </v-snackbar>
  </v-dialog>
</template>

<script>
import { mapGetters } from 'vuex'

export default{
  data(){
    return {
      password: '',
      cPassword: '',
      snackbar: false,
      message: ''
    }
  },
  computed: { ...mapGetters({ user: 'Account/user' }) },
  methods: {
    async changePassword(){
      try{
        const { data } = await this.$store.dispatch('Account/recoveryChange', this.password)
        this.message = data.recoveryChange.message
        this.snackbar = true
      }catch(e){
        this.message = e.split('\"')[1]
        this.snackbar = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .card-custom{
    h2{
      color: #333;
    }

    p{
      color: #555;
    }

    b{
      color: red;
    }
  }
</style>
