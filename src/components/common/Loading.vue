<template>
  <v-dialog
    v-model="loading"
    persistent
    width="200"
  >
    <v-card>
      <v-card-text class="content">
        <v-progress-circular
          indeterminate
          color="#3498db"
        />
        <b>Carregando...</b>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'

  export default {
    computed: { ...mapGetters({ loading: 'loading' }) }
  }
</script>

<style lang="scss" scoped>
  .content{
    text-align: center;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    b{
      margin-top: 5px;
    }
  }
</style>
