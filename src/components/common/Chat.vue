<template>
    <div class="chat-column" :style="bodyChat" id="chat-column">
      <header class="header" id="header" @click="changeState">
        <h1> <PERSON><PERSON> </h1>
        <p>Seja bem-vindo <i class="em em-slightly_smiling_face"></i></p>

        <v-btn icon class="closeButton" @click="close">
          <v-icon color="#fff">close</v-icon>
        </v-btn>
      </header>

      <div id="bodyMessage"  v-show="bodyChat.state">
        <div id="scrollingChat">
          <div
            class="sendItem"
            :class="{robot: item.send == 'robot', client: item.send == 'client'}"
            v-for="(item, index) in messages"
            :key="index"
          >
            <div class="profile">
              <img src="@/assets/robot.png">
            </div>
            <div class="message">
              {{item.text}}
              <iframe style="margin-top: 15px;" v-if="item.video" width="100%" height="315" :src="item.video" frameborder="0" allow="encrypted-media" allowfullscreen></iframe>
            </div>
          </div>
        </div>

        <div class="mesInput">
          <input
            id="textInput"
            class="input responsive-column"
            placeholder="Qual a sua dúvida?"
            type="text"
            @keyup="verificar"
            v-model="message"
            @keyup.enter="send(true)"
          >

          <div class="send" :class="{display: !sendIcon}" @click="send(true)">
            <img src="@/assets/send.png">
          </div>

          <div class="alert" v-if="typing"><b>Signa</b> está digitando...</div>
        </div>
      </div>
    </div>
</template>

<script>
import axios from 'axios'
import ip from 'ip'
export default {
  data () {
    return {
      sendIcon: false,
      message: null,
      bodyChat: {
        state: true,
        height: '450px'
      },
      tamanho: {
        height: 800
      },
      typing: false,
      messages: [],
      ip: null
    }
  },
  mounted () {
    this.send()
    let self = this
    // this.getIPs(function(ip){ self.ip = ip })
  },
  updated () {
    var el = document.getElementById('scrollingChat')
    el.scrollTop = el.scrollHeight
  },
  methods: {
    verificar () {
      if (this.message.length > 0) {
        this.sendIcon = true
      } else {
        this.sendIcon = false
      }
    },
    changeState () {
      this.bodyChat.height = this.bodyChat.state ? '55px' : '450px'
      this.bodyChat.state = !this.bodyChat.state
    },
    close () {
      this.$store.dispatch('Users/chat')
    },
    getIPs (callback) {
			    var ip_dups = {}

			    // compatibility for firefox and chrome
			    var RTCPeerConnection = window.RTCPeerConnection ||
			        window.mozRTCPeerConnection ||
			        window.webkitRTCPeerConnection
			    var useWebKit = !!window.webkitRTCPeerConnection

			    // bypass naive webrtc blocking using an iframe
			    if (!RTCPeerConnection) {
			        // NOTE: you need to have an iframe in the page right above the script tag
			        //
			        // <iframe id="iframe" sandbox="allow-same-origin" style="display: none"></iframe>
			        // <script>...getIPs called in here...
			        //
			        var win = iframe.contentWindow
			        RTCPeerConnection = win.RTCPeerConnection ||
			            win.mozRTCPeerConnection ||
			            win.webkitRTCPeerConnection
			        useWebKit = !!win.webkitRTCPeerConnection
			    }

			    // minimal requirements for data connection
			    var mediaConstraints = {
			        optional: [{ RtpDataChannels: true }]
			    }

			    var servers = { iceServers: [{ urls: 'stun:stun.services.mozilla.com' }] }

			    // construct a new RTCPeerConnection
			    var pc = new RTCPeerConnection(servers, mediaConstraints)

			    function handleCandidate (candidate) {
			        // match just the IP address
			        var ip_regex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/
			        var ip_addr = ip_regex.exec(candidate)[1]

			        // remove duplicates
			        if (ip_dups[ip_addr] === undefined) { callback(ip_addr) }

			        ip_dups[ip_addr] = true
			    }

			    // listen for candidate events
			    pc.onicecandidate = function (ice) {
			        // skip non-candidate events
			        if (ice.candidate) { handleCandidate(ice.candidate.candidate) }
			    }

			    // create a bogus data channel
			    pc.createDataChannel('')

			    // create an offer sdp
			    pc.createOffer(function (result) {
			        // trigger the stun server request
			        pc.setLocalDescription(result, function () {}, function () {})
			    }, function () {})

			    // wait for a while to let everything done
			    setTimeout(function () {
			        // read candidate info from local description
			        var lines = pc.localDescription.sdp.split('\n')

			        lines.forEach(function (line) {
			            if (line.indexOf('a=candidate:') === 0) { handleCandidate(line) }
			        })
			    }, 1000)
    },
    tratarMensagem (msg, flag) {
      let mensagem = msg.split('VIDEO|')
      let retorno = ''

      if (flag) {
        retorno = mensagem.length > 1 ? mensagem[1] : false
      } else {
        retorno = mensagem[0]
      }

      return retorno
    },
    send (cliente = false) {
      this.typing = true

      if (cliente) {
        this.messages.push({
          text: this.message,
          send: 'client'
        })
      }
      axios.get('https://api.signainfo.com.br/message', {
        params: {
          texto: this.message,
          Usuario: 'asp.signa',
          UsuarioId: 1,
          Empresa: 'SIGNA.C',
          ip: this.ip
        }
      })
        .then(res => {
          this.typing = false
          this.message = ''
          this.messages.push({
            text: this.tratarMensagem(res.data.output.text[0], false),
            send: 'robot',
            video: this.tratarMensagem(res.data.output.text[0], true)
          })

          setTimeout(() => {
            this.scroll()
          }, 100)
        })
        .catch(err => {

        })
    },
    scroll () {
      window.scrollTo(0, document.getElementById('scrollingChat').scrollHeight || document.getElementById('scrollingChat').scrollHeight)
    }
  }
}
</script>

<style scoped>
input{
	outline: none
}

.chat-column{
	margin: auto;
	position: fixed;
	bottom: 0;
	text-align: left;
  width: 300px;
  right: 20px;
	transition: .5s;
  display: flex;
  justify-content: space-between;
}

#scrollingChat {
	overflow-y: auto;
	overflow-x: hidden;
	background: url('../../assets/bg.jpg');
	padding-top: 65px;
	padding-bottom: 50px;
  height: 450px;
	min-height: 100% !important;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.header{
	width: 300px;
	height: 55px;
	padding: 10px;
	text-align: center;
	background: #56CCF2;
  background: -webkit-linear-gradient(left, #2F80ED, #56CCF2);
  background: -o-linear-gradient(left, #2F80ED, #56CCF2);
  text-align: center;
  color: white;
  cursor: pointer;
  position: fixed;
}

.header h1{
	font-size: 0.9em;
	margin: 0;
}

.header p{
	margin: 0;
	font-size: 11px;
}

.message-inner p{
	font-size: 13px !important;
}

.from-watson.top p{
	border-radius: 0 15px 15px 15px !important;
}

.anex{
	float: left;
	width: 30px;
	height: 30px;
	background: #56CCF2;
  background: -webkit-linear-gradient(left, #2F80ED, #56CCF2);
  background: -o-linear-gradient(left, #2F80ED, #56CCF2);
  border-radius: 50%;
  padding: 7px;
  box-shadow: 0px 3px 5px #56CCF2;
  cursor: pointer;
}

.anex img{
	width: 100%;
}

.mesInput{
	background: white;
	float: left;
	padding: 5px;
	width: 300px;
	position: fixed;
	bottom: 0;
	height: 55px;
}

.mesInput .alert{
	color: #555;
	font-size: 12px;
	width: 100%;
	float: left;
	margin-left: 5px;
	margin-top: 5px;
	position: absolute;
	bottom: 2px;
}

.mesInput input{
	background: #edf9ff !important;
	border: 1px solid #dbf1ff !important;
	width: calc(100% - 40px) !important;
	height: 30px !important;
	border-radius: 20px;
	padding-left: 10px !important;
	color: rgba(52, 73, 94,1.0) !important;
	float: left;
}

.mesInput .send{
	float: left;
	padding-left: 5px;
	cursor: pointer;
}

.display{
	display: none;
}

.mesInput input::placeholder{
	color: #AAA;
}

#bodyMessage{
  box-shadow: 0 0 5px #E1E1E1;
  height: 100%;
  width: 100%;
	transition: .5s;
  display: flex;
  justify-content: space-between;
}

.sendItem{
	width: 100%;
	float: left;
}

.sendItem .profile{
	width: 55px;
	padding-left: 10px;
	float: left;
	margin-right: 5px;
}

img{
	width: 100%;
}

.message{
	float: left;
	max-width: calc(100% - 70px);
	min-width: auto;
	padding: 10px 10px;
	margin-top: 5px;
	font-size: 14px;
  word-wrap: break-word
}

.sendItem.robot .message{
	background: #00c6ff;
  background: -webkit-linear-gradient(to top, #00c5ff, #00c6ff);
  background: linear-gradient(to top, #00c5ff, #00c6ff);
  color: #FFF;
  font-weight: 600;
  border-radius: 0 15px 15px 15px !important;
}

.sendItem.client .profile{
	float: right;
}

.sendItem.client .message{
	background: #70bbd1;
  color: #FFF;
  font-weight: 600;
  border-radius: 15px 0px 15px 15px;
	float: right;
}

.closeButton{
  position: absolute;
  right: 0;
  top: 5px;
}

</style>
