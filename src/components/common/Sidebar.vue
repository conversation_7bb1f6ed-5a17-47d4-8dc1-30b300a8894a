<template>
  <v-navigation-drawer
    :clipped="$vuetify.breakpoint.lgAndUp"
    :value="drawer"
    fixed
    app
  >
    <v-toolbar flat>
      <v-text-field
        flat
        solo
        hide-details
        prepend-inner-icon="search"
        :label="$t('search')"
        background-color="#e1e1e1"
        v-model="query"
      />
    </v-toolbar>

    <v-list dense class="pt-0" >
      <v-list-tile @click="$router.push({ name: 'dashboard' })" class="favourites">
        <v-list-tile-action>
          <v-icon color="rgb(52, 152, 219)">home</v-icon>
        </v-list-tile-action>
        <v-list-tile-title>Favoritos</v-list-tile-title>
      </v-list-tile>
    <div class="menuPanel">
      <v-list-group
        v-for="(item, index) in filter"
        :key="`item_menu_${item.id}_${index}`"
      >
        <template v-slot:activator>
          <v-list-tile>
            <v-list-tile-action>
              <Icon :name="item.icon" :size="22" color="rgb(52, 152, 219)" />
            </v-list-tile-action>
            <v-list-tile-title>{{ item.name }}</v-list-tile-title>
          </v-list-tile>
        </template>

        <v-list-tile
          v-for="(child, index) in item.children"
          :key="`item_submenu_${child.id}_${index}`"
          @click="mountItem(child.href, child.fatherString, child.meString)"
        >
          <v-list-tile-action>
            <v-icon />
          </v-list-tile-action>
          <v-list-tile-title v-text="child.shortName" />
        </v-list-tile>
      </v-list-group>
    </div>
    </v-list>
  </v-navigation-drawer>
</template>

<script>
import gql from "graphql-tag";
import { getStorage } from "@/utils/localStorage";
import { mapGetters } from "vuex";
import Icon from "@/components/common/Icon";

export default {
  data() {
    return {
      query: ""
    };
  },
  components: { Icon },
  computed: {
    ...mapGetters({
      menu: "Menu/list"
    }),
    drawer() {
      return this.$store.state.Users.drawer;
    },
    filter() {
      return JSON.parse(JSON.stringify(this.menu))
        .map(item => {
          item.children = item.children.filter(item =>
            item.shortName.toLowerCase().match(RegExp(this.query.toLowerCase()))
          );
          return item;
        })
        .filter(item => item.children.length > 0);
    }
  },
  methods: {
    mountItem(item, father, children) {
      this.$router.push({ name: 'page', params: { father, children } })
      this.$store.dispatch('Users/mountPage', item)
    }
  }
};
</script>
<style lang="scss" scoped>
  aside{
    overflow-y: hidden;
  }
  .favourites {
    border-bottom: 1px solid;
    border-image: linear-gradient(to right, #b6c3ff00, #bfd7eb7a) 1;
    border-width: 1px;
    margin: 1px;
  }
  .startSubmenu0{
    box-shadow: inset 5px 9px 10px -8px #575757a6;
  }

</style>
<style lang="scss">
  .v-list__group__items > div:hover{
    // background: #0000001c;
    transform: translateX(7px);
    border-image: linear-gradient(to right, #b6c3ff00, #3972a2) 1;
    border-bottom: 1px solid;
    border-top: 1px solid;
  }
  .v-list__group__items {
    // margin-top: 3px;
    background: #f7f7f7;
    box-shadow: inset 5px 0px 8px 3px #cfcfcf9e;
  }
  // .v-list__group__items:hover{
  //   background: #0000000a;
  // }
  .theme--light.v-list .v-list__group__header:hover {
    background: #f5f5f5;
    // box-shadow: 0px 0px 4px 0px #86869f;
  }
  // .v-list__group__header--active{
  // }

  .v-list__group__items > div::before {
      content: "";
      position: absolute;
      width: 7px;
      height: 100%;
      background: #629ca7;
      left: -7px;
      top: 0;
      transition: .3s;
      opacity: 0;
  }
  .v-list__group__items > div:hover::before {
      opacity: 1;
  }
  .v-list__group__header{
    transition: all 0.3s;
    // border-bottom: 1px solid rgba(0,0,0,0.05);
    // border-top: 1px solid rgba(255,255,255,0.05);
    border-bottom: 1px solid;
    /*border-image: linear-gradient(to right, #fefeff14, #a3c3dfbd) 1;*/
    border-image: linear-gradient(to right, #b6c3ff00, #bfd7eb7a) 1;
    border-width: 1px;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10000000;
  }
  .v-navigation-drawer > .v-list .v-list__tile, .v-list__group__items > div{
    transition: transform 0.3s /*, background 0.3s*/;
  }

  .theme--light.v-list .v-list__group--active:before, .theme--light.v-list .v-list__group--active:after {
    background: none;
  }
  .menuPanel{
    height: 90vh;
    // -webkit-overflow-y: overlay;
    // -moz-overflow-y: auto;
    overflow-y: auto;
    overflow-X: clip;
  }
  .v-list__group__items > div:active {
      // transform: translateX(15px) scale(0.96, 0.96);
      background: linear-gradient(to right,aliceblue,#4578974a);
  }

  @supports (overflow-y: overlay) {
    .menuPanel {
      overflow-y: overlay;
    }
  }
  @media (max-height: 1500px)
  {
    .menuPanel {
      height: 85vh;
    }
  }
  @media (max-height: 1050px)
  {
    .menuPanel {
      height: 79vh;
    }
  }
  @media (max-height: 970px) and (min-height: 940px)
  {
    .menuPanel {
        height: calc(80vh + 30px);
    }
  }
  @media (max-height: 770px)
  {
    .menuPanel {
      height: 71vh;
    }
  }
  @media (max-height: 600px)
  {
    .menuPanel {
      height: 65vh;
    }
  }
  @media (max-height: 500px)
  {
    .menuPanel {
      height: 60vh;
    }
  }
  @media (max-height: 350px)
  {
    .menuPanel {
      height: 44vh;
    }
  }
</style>
