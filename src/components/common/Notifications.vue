<template>
  <v-menu
    offset-y
    content-class="mt-2 arrow-top"
    left
    origin="top right"
    :close-on-content-click="false"
    transition="scale-transition"
    :position-y="50"
    :max-width="300"
  >
    <v-btn icon slot="activator">
      <v-icon color="#fff">notifications</v-icon>
    </v-btn>

    <v-list subheader class="listItems">
      <v-list-tile>
        <v-list-tile-content>
          <v-list-tile-title style="display: flex; justify-content: center; align-items: center;">{{ $t('lastAccess') }}</v-list-tile-title>
        </v-list-tile-content>
      </v-list-tile>
    </v-list>

    <v-divider />

    <v-list
      class="listItems"
      v-for="(item, index) in notifications"
      :key="item.id"
      :max-height="20"
    >
      <v-list-tile v-if="index < 3" class="listTile" avatar>
        <v-list-tile-avatar>
          <v-icon avatar color="#444">
            access_time
          </v-icon>
        </v-list-tile-avatar>
        <v-list-tile-content>
          <!-- <v-list-tile-title class="listItems__title">Ultimo acesso</v-list-tile-title> -->
          <v-list-tile-sub-title class="listItems__desc">
            <p style="margin-top: 15px;">
              {{ item.message }}
            </p>
          </v-list-tile-sub-title>
        </v-list-tile-content>
      </v-list-tile>
      <v-divider
        v-if="index < 3"
        class="listItems__divider"
      />
    </v-list>
  </v-menu>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters({ notifications: 'Account/notification' })
  }
}
</script>

<style lang="scss" scoped>
@import 'essencials';

img{
  width: 100%;
}

.arrow-top{
  contain: initial;
  overflow: initial;
  box-shadow: 0 0 10px #ccc;
}

.arrow-top::before{
  top: -16px;
  right: 5px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(255, 255, 255, 0);
  border-bottom-color: #fff;
  border-width: 8px;
  margin-left: -8px;
}

.listItems{
  padding: 0;
  &__title{
    color: #444;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 600;
  }

  &__desc{
    p{
      color: #666;
      font-weight: 500;
      font-size: 12px;
    }
  }

  &__divider{
    border-color: #f1f1f1;
  }
}

</style>
