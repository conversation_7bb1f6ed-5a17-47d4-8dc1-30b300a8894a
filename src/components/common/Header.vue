<template>
  <v-toolbar
    :clipped-left="$vuetify.breakpoint.lgAndUp"
    color="#3498db"
    dark
    app
    fixed
    flat
    height="55"
  >

    <v-toolbar-title class="ml-0">
      <v-toolbar-side-icon @click="drawerChange">
        <div class="wrapper-menu" :class="drawer ? 'open' : ''">
          <div class="line-menu half start"></div>
          <div class="line-menu"></div>
          <div class="line-menu half end"></div>
        </div>
      </v-toolbar-side-icon>
    </v-toolbar-title>

    <div class="main_logo">
      <img :src="logo" class="logo" @error="replaceByDefault">
      <img src="/asp/images/LogoEmpresa.png" srcset="/asp/images/LogoEmpresa_100w.png 100w" class="logo_empresa" onerror="this.style.display='none'"/>
    </div>
    <v-spacer class="headerText" v-text="headerText" />
    <Language />
    <ChatIcon />
    <Notifications />
    <Settings />
  </v-toolbar>
</template>

<script>
import Notifications from '@/components/common/Notifications'
import Settings from '@/components/common/Settings'
import ChatIcon from '@/components/common/ChatIcon'
import Language from '@/components/common/Language'
import { getStorage } from '@/utils/localStorage'
import { mapGetters } from 'vuex'

export default {
  components: { Notifications, Settings, ChatIcon, Language },
  computed: {
    ...mapGetters({headerText: 'Menu/headerText'}),
    drawer(){
      return this.$store.state.Users.drawer
    },
    logo(){
      return getStorage('logo')
    }
  },
  methods: {
    drawerChange (){
      this.$store.dispatch('Users/changeDrawer', !this.drawer)
    },
    replaceByDefault(e) {
      e.target.src = require('@/assets/logoMenu.png')
    }
  }
}
</script>

<style lang="scss" scoped>
  .main_logo{
    display: flex;
    align-items: center;
    gap: 20px;
    margin-left: 15px;
  }
  .logo{
    max-width: 50px;
  }
  .logo_empresa{
    max-width: 100px;
  }
  .wrapper-menu {
    width: 20px;
    height: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    transition: transform 330ms ease-out;
  }

  .line-menu {
    background-color: #fff;
    border-radius: 5px;
    width: 100%;
    height: 4px;
  }

  .line-menu.half {
    width: 50%;
  }

  .line-menu.start {
    transition: transform 330ms cubic-bezier(0.54, -0.81, 0.57, 0.57);
    transform-origin: right;
  }

  .open .line-menu.start {
    transform: translateX(10px);
  }

  .line-menu.end {
    align-self: flex-end;
    transition: transform 330ms cubic-bezier(0.54, -0.81, 0.57, 0.57);
    transform-origin: left;
  }

  .open .line-menu.end {
    transform:  translateX(-10px);
  }
  .headerText{
    font-size: 2rem;
    color: #fff;
    text-align: center;
    flex-grow: 1;
  }
</style>
