<template>
  <v-menu
    offset-y
    content-class="mt-2 arrow-top"
    left
    origin="top right"
    transition="scale-transition"
    :position-y="50"
  >
    <template v-slot:activator="{ on }">
      <v-btn icon v-on="on">
        <Icon :name="`${language}-flag`" :size="22" color="#fff" />
      </v-btn>
    </template>
    <v-list>
      <v-list-tile @click="localeChanging('es')" :class="language === 'es' ? 'active' : ''">
        <v-list-tile-title class="textAlign">
          <Icon name="es-flag" :size="22" color="#fff" />
          Espanhol
        </v-list-tile-title>
      </v-list-tile>
      <v-list-tile @click="localeChanging('pt')" :class="language === 'pt' ? 'active' : ''">
        <v-list-tile-title class="textAlign">
          <Icon name="pt-flag" :size="22" color="#fff" />
          Portugues
        </v-list-tile-title>
      </v-list-tile>
    </v-list>
  </v-menu>
</template>

<script>
  import Icon from '@/components/common/Icon'
  import { setStorage } from '@/utils/localStorage'

  export default {
    components: { Icon },
    computed: {
      language(){
        return this.$i18n.locale === undefined ? 'pt' : this.$i18n.locale
      }
    },
    methods: {
      localeChanging(language){
        setStorage('language', language)
        this.$store.dispatch('Account/language', language)
        this.$i18n.locale = language
      }
    }
  }
</script>

<style lang="scss" scoped>
  .active{
    background: #f5f5f5;
  }

  .arrow-top{
    contain: initial;
    overflow: initial;
    box-shadow: 0 0 10px #ccc;
  }

  .arrow-top::before{
    top: -16px;
    right: 5px;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: #fff;
    border-width: 8px;
    margin-left: -8px;
  }

  .textAlign{
    display: flex;
    justify-content: flex-start;
    font-size: 14px;
    svg{
      margin-right: 10px;
    }
  }
</style>
