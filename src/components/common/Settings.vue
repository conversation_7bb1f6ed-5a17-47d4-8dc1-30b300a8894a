<template>
  <v-menu
    offset-y
    offset-x
    content-class="mt-2 arrow-top"
    lazy
    :position-y="50"
    transition="scale-transition"
    right
    origin="top right"
    absolute
    :close-on-content-click="false"
    :min-width="200"
  >
      <v-btn flat slot="activator" icon>
        <v-avatar
          size="35px"
        >
          <img :src="/(.*\w+\.\w{3})|(data:image\/\w{3};)/g.exec(user.picture) ? user.picture : require('@/assets/user.png')" @error="replaceByDefault" />
        </v-avatar>
      </v-btn>

      <v-divider />

      <v-list>
        <v-list-tile class="tile" @click="profile">
          <v-list-tile-avatar>
            <v-icon>person_pin</v-icon>
          </v-list-tile-avatar>
          <v-list-tile-title>{{ $t('profile')  }}</v-list-tile-title>
        </v-list-tile>

        <v-list-tile v-on:click.native="logout" class="v-list__tile--link">
          <v-list-tile-avatar>
            <v-icon>power_settings_new</v-icon>
          </v-list-tile-avatar>
          <v-list-tile-title>{{ $t('logout') }}</v-list-tile-title>
        </v-list-tile>
      </v-list>
    </v-menu>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters({ user: 'Account/user' })
  },
  methods: {
    logout(){
      try {
        this.$router.push({ name: 'login_form' })
        this.$store.dispatch('Users/logout')        
      } catch (error) {}
    },
    profile(){
      this.$store.dispatch('Users/changeStatusModal', { name: 'profile' })
    },
    replaceByDefault(e) {
      e.target.src = require('@/assets/user.png')
    }
  }
}
</script>

<style lang="scss" scoped>
@import 'essencials';

img{
  width: 100%;
}

.username{
  margin-left: 10px;
  text-transform: capitalize;
}

.arrow-top{
  contain: initial;
  overflow: initial;
  box-shadow: 0 0 10px #ccc;
}

.tile{
  border-bottom: 1px solid #f1f1f1;
}

.tile:last-child{
  border-bottom: 0;
}

.arrow-top::before{
  top: -16px;
  right: 15px;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(255, 255, 255, 0);
  border-bottom-color: #fff;
  border-width: 8px;
  margin-left: -8px;
}
</style>
