<template>
  <svg
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 512 512"
    xml:space="preserve"
    :width="size"
    :height="size"
    :aria-labelledby="name"
  >
    <g :fill="color">
      <g v-if="name === 'comercial'">
        <g>
          <g>
            <path d="M123.733,130.133c-17.067-17.067-89.6-21.333-113.067-23.467c-2.133,0-4.267,0-6.4,2.133C2.133,110.933,0,115.2,0,117.333
              v192C0,315.733,4.267,320,10.667,320h64c4.267,0,8.533-2.133,10.667-6.4c0-6.4,38.4-119.467,42.667-174.933
              C128,136.533,128,132.267,123.733,130.133z"/>
          </g>
        </g>
        <g>
          <g>
            <path d="M352,181.333c-21.333-6.4-40.533-14.933-57.6-21.333c-38.4-17.067-55.467-8.533-89.6,25.6
              c-14.933,14.933-25.6,36.267-23.467,44.8c0,2.133,0,2.133,4.267,4.267c10.667,4.267,25.6,6.4,40.533-17.067
              c2.133-2.133,4.267-4.267,8.533-4.267c6.4,0,8.533-2.133,14.933-4.267c4.267-2.133,8.533-4.267,14.933-6.4
              c2.133,0,2.133,0,4.267,0c2.133,0,6.4,2.133,8.533,2.133C288,215.467,307.2,230.4,326.4,247.467
              c29.867,23.467,59.733,49.067,74.667,68.267h2.133C388.267,273.067,362.667,200.533,352,181.333z"/>
          </g>
        </g>
        <g>
          <g>
            <path d="M501.333,128c-83.2,0-130.133,21.333-132.267,21.333c-2.133,2.133-4.267,4.267-6.4,6.4c0,2.133,0,6.4,2.133,8.533
              c12.8,21.333,55.467,138.667,61.867,168.533c2.133,4.267,6.4,8.533,10.667,8.533h64c6.4,0,10.667-4.267,10.667-10.667v-192
              C512,132.267,507.733,128,501.333,128z"/>
          </g>
        </g>
        <g>
          <g>
            <path d="M386.133,337.067c-8.533-19.2-44.8-46.933-76.8-72.533C292.267,249.6,275.2,236.8,262.4,226.133
              c-2.133,2.133-6.4,2.133-6.4,4.267c-6.4,2.133-8.533,4.267-17.067,4.267C221.867,256,200.533,264.533,177.067,256
              c-10.667-2.133-17.067-10.667-19.2-19.2c-4.267-21.333,14.933-51.2,29.867-66.133h-42.667c-8.533,42.667-23.467,98.133-34.133,128
              c8.533,8.533,17.067,19.2,23.467,23.467c40.533,34.133,87.467,68.267,96,74.667c6.4,4.267,19.2,8.533,25.6,8.533
              c2.133,0,4.267,0,6.4,0L228.267,371.2c-4.267-4.267-4.267-10.667,0-14.933s10.667-4.267,14.933,0l42.667,42.667
              c4.267,4.267,8.533,2.133,12.8,2.133c6.4-2.133,8.533-6.4,10.667-12.8L260.267,339.2c-4.267-4.267-4.267-10.667,0-14.933
              s10.667-4.267,14.933,0l53.333,53.333c2.133,2.133,10.667,2.133,17.067,0c2.133-2.133,6.4-4.267,8.533-8.533L294.4,309.333
              c-4.267-4.267-4.267-10.667,0-14.933s10.667-4.267,14.933,0l61.867,61.867c4.267,2.133,8.533,0,12.8-2.133
              C386.133,352,390.4,345.6,386.133,337.067z"/>
          </g>
        </g>
      </g>
      <g v-if="name === 'operacional'">
        <path d="m236.515625 477.066406c11.410156 20.816406 33.523437 34.933594 58.9375 34.933594 25.445313 0 47.582031-14.152344 58.980469-35.015625-1.082032.046875-2.164063.082031-3.257813.082031zm0 0"/><path d="m.5 203.71875h85.753906v234.074219h-85.753906zm0 0"/><path d="m351.175781 447.066406c23.613281 0 42.828125-19.210937 42.828125-42.828125 0-10.609375-3.882812-20.335937-10.300781-27.828125 6.417969-7.488281 10.300781-17.214844 10.300781-27.824218 0-10.613282-3.882812-20.339844-10.300781-27.828126 6.417969-7.492187 10.300781-17.21875 10.300781-27.828124 0-10.613282-3.882812-20.339844-10.300781-27.828126 6.417969-7.492187 10.300781-17.214843 10.300781-27.828124 0-23.617188-19.214844-42.828126-42.828125-42.828126h-111.257812v-30s113.089843.035157 114 .070313c22.652343-17.503906 37.253906-44.917969 37.253906-75.757813 0-40.171874-24.746094-74.554687-59.824219-88.757812v71.789062l-35.894531 23.933594-35.894531-22.921875v-72.800781c-35.078125 14.199219-59.828125 48.585938-59.828125 88.753906 0 15.425782 3.65625 29.988282 10.136719 42.886719l-66.589844 90.632813h-27.027344v196.964843h35.867188l18.554687 27.828125zm0 0"/>
      </g>
      <g v-if="name === 'tracking'">
        <path id="Facebook_Places" d="M356.208,107.051c-1.531-5.738-4.64-11.852-6.94-17.205C321.746,23.704,261.611,0,213.055,0
          C148.054,0,76.463,43.586,66.905,133.427v18.355c0,0.766,0.264,7.647,0.639,11.089c5.358,42.816,39.143,88.32,64.375,131.136
          c27.146,45.873,55.314,90.999,83.221,136.106c17.208-29.436,34.354-59.259,51.17-87.933c4.583-8.415,9.903-16.825,14.491-24.857
          c3.058-5.348,8.9-10.696,11.569-15.672c27.145-49.699,70.838-99.782,70.838-149.104v-20.262
          C363.209,126.938,356.581,108.204,356.208,107.051z M214.245,199.193c-19.107,0-40.021-9.554-50.344-35.939
          c-1.538-4.2-1.414-12.617-1.414-13.388v-11.852c0-33.636,28.56-48.932,53.406-48.932c30.588,0,54.245,24.472,54.245,55.06
          C270.138,174.729,244.833,199.193,214.245,199.193z"/>
      </g>
      <g v-if="name === 'frota'">
        <path d="M541.322,500.219v-94.372c0-20.277-16.438-36.716-36.715-36.716h-9.598V24.598c0-3.082-1.547-5.958-4.117-7.657
          L467.587,1.537c-6.103-4.033-14.239,0.342-14.239,7.657v110.652l-6.945-18.734c-9.34-25.196-33.373-41.918-60.245-41.918H225.702
          c-27.03,0-51.169,16.916-60.394,42.323l-6.655,18.329V9.194c0-7.314-8.137-11.69-14.24-7.657L121.107,16.94
          c-2.571,1.699-4.118,4.575-4.118,7.657v344.534h-9.597c-20.277,0-36.715,16.438-36.715,36.716v94.372H55.035
          c-5.069,0-9.178,4.109-9.178,9.179v50.743c0,5.069,4.109,9.179,9.178,9.179h39.598v24.322c0,10.139,8.219,18.357,18.358,18.357
          h48.645c10.139,0,18.358-8.219,18.358-18.357V569.32h252.014v24.322c0,10.139,8.22,18.357,18.357,18.357h48.646
          c10.139,0,18.357-8.219,18.357-18.357V569.32h39.598c5.07,0,9.179-4.11,9.179-9.179v-50.742c0-5.07-4.109-9.179-9.179-9.179
          L541.322,500.219L541.322,500.219z M170.814,170.975h270.372v90.44H170.814V170.975z M164.527,474.533H133.17
          c-9.581,0-17.348-7.768-17.348-17.349v-0.438c0-9.581,7.767-17.348,17.348-17.348h31.356c9.581,0,17.348,7.767,17.348,17.348v0.438
          C181.875,466.765,174.108,474.533,164.527,474.533z M368.398,479.648H243.602c-10.139,0-18.358-8.22-18.358-18.357V344.976
          c0-10.138,8.219-18.357,18.358-18.357h124.796c10.138,0,18.357,8.22,18.357,18.357v116.314
          C386.756,471.428,378.536,479.648,368.398,479.648z M478.829,474.533h-31.356c-9.58,0-17.348-7.768-17.348-17.349v-0.438
          c0-9.581,7.768-17.348,17.348-17.348h31.356c9.581,0,17.349,7.767,17.349,17.348v0.438
          C496.178,466.765,488.41,474.533,478.829,474.533z M365.607,393.801H246.099c-5.019,0-9.087-4.068-9.087-9.088v-0.184
          c0-5.019,4.068-9.086,9.087-9.086h119.508c5.019,0,9.087,4.067,9.087,9.086v0.184C374.694,389.733,370.626,393.801,365.607,393.801
          z M365.607,357.085H246.099c-5.019,0-9.087-4.068-9.087-9.087v-0.184c0-5.018,4.068-9.086,9.087-9.086h119.508
          c5.019,0,9.087,4.068,9.087,9.086v0.184C374.694,353.017,370.626,357.085,365.607,357.085z M365.607,467.232H246.099
          c-5.019,0-9.087-4.068-9.087-9.087v-0.184c0-5.019,4.068-9.087,9.087-9.087h119.508c5.019,0,9.087,4.068,9.087,9.087v0.184
          C374.694,463.164,370.626,467.232,365.607,467.232z M365.607,430.516H246.099c-5.019,0-9.087-4.068-9.087-9.086v-0.184
          c0-5.019,4.068-9.087,9.087-9.087h119.508c5.019,0,9.087,4.068,9.087,9.087v0.184C374.694,426.448,370.626,430.516,365.607,430.516
          z"
        />
      </g>
      <g v-if="name === 'faturamento'">
        <path d="m496.988281 220c-3.25 0-6.523437-1.050781-9.273437-3.222656l-231.714844-182.675782-231.710938 182.675782c-6.507812 5.132812-15.941406 4.015625-21.066406-2.492188-5.128906-6.503906-4.015625-15.9375 2.492188-21.066406l241-190c5.445312-4.292969 13.125-4.292969 18.570312 0l241 190c6.507813 5.128906 7.621094 14.5625 2.492188 21.066406-2.957032 3.753906-7.351563 5.714844-11.789063 5.714844zm0 0"/><path d="m497 512h-482c-8.28125 0-15-6.71875-15-15 0-8.285156 6.71875-15 15-15h482c8.285156 0 15 6.714844 15 15 0 8.28125-6.714844 15-15 15zm0 0"/><path d="m469.136719 240.335938-213.136719-168.03125-106.570312 84.015624-119.429688 94.15625v201.523438h452v-201.523438zm-273.136719 50.664062c24.8125 0 45 20.1875 45 45 0 19.554688-12.539062 36.226562-30 42.417969v7.582031c0 8.28125-6.714844 15-15 15-8.28125 0-15-6.71875-15-15v-5h-15c-8.28125 0-15-6.71875-15-15 0-8.285156 6.71875-15 15-15h30c8.273438 0 15-6.730469 15-15 0-8.273438-6.726562-15-15-15-24.8125 0-45-20.1875-45-45 0-19.554688 12.542969-36.230469 30-42.421875v-7.578125c0-8.285156 6.71875-15 15-15 8.285156 0 15 6.714844 15 15v5h15c8.285156 0 15 6.714844 15 15 0 8.28125-6.714844 15-15 15h-30c-8.269531 0-15 6.726562-15 15 0 8.269531 6.730469 15 15 15zm120 0c24.8125 0 45 20.1875 45 45 0 19.554688-12.539062 36.226562-30 42.417969v7.582031c0 8.28125-6.714844 15-15 15s-15-6.71875-15-15v-5h-15c-8.285156 0-15-6.71875-15-15 0-8.285156 6.714844-15 15-15h30c8.273438 0 15-6.730469 15-15 0-8.273438-6.726562-15-15-15-24.8125 0-45-20.1875-45-45 0-19.554688 12.542969-36.230469 30-42.421875v-7.578125c0-8.285156 6.714844-15 15-15s15 6.714844 15 15v5h15c8.285156 0 15 6.714844 15 15 0 8.28125-6.714844 15-15 15h-30c-8.269531 0-15 6.726562-15 15 0 8.269531 6.730469 15 15 15zm0 0"/>      </g>
      <g v-if="name === 'financeiro'">
        <path d="M437.019,74.98C388.668,26.629,324.381,0,256,0C187.62,0,123.333,26.629,74.981,74.98C26.629,123.332,0,187.619,0,256
          s26.628,132.668,74.981,181.02C123.333,485.371,187.62,512,256,512c68.381,0,132.668-26.629,181.02-74.98
          C485.372,388.668,512,324.381,512,256S485.372,123.332,437.019,74.98z M272.068,367.4H271v33.201c0,8.284-6.715,15-15,15
          c-8.283,0-15-6.716-15-15V367.4h-33.199c-8.283,0-15-6.716-15-15s6.717-15,15-15h64.268c18.306,0,33.199-14.894,33.199-33.2
          c0-18.306-14.894-33.2-33.2-33.2h-32.135c-34.848,0-63.199-28.351-63.199-63.199c0-34.849,28.352-63.2,63.199-63.2H241v-33.2
          c0-8.284,6.717-15,15-15c8.285,0,15,6.716,15,15v33.2h33.201c8.283,0,15,6.716,15,15s-6.717,15-15,15h-64.268
          c-18.307,0-33.199,14.893-33.199,33.2c0,18.306,14.893,33.199,33.199,33.199h32.135c34.848,0,63.199,28.352,63.199,63.2
          S306.916,367.4,272.068,367.4z"
        />
      </g>
      <g v-if="name === 'infra_estrutura'">
        <path d="M335.842,261.456h-20.876c-3.916-20.76-12.334-40.449-24.619-57.601l14.492-14.495c1.41-1.411,2.203-3.325,2.203-5.324
          c0-1.995-0.793-3.91-2.203-5.319l-25.292-25.291c-2.945-2.936-7.704-2.936-10.65,0l-14.917,14.922
          c-17.006-11.628-36.311-19.53-56.495-23.109v-21.193c0-4.156-3.374-7.529-7.529-7.529h-35.768c-4.156,0-7.529,3.374-7.529,7.529
          v21.193c-20.345,3.609-39.795,11.598-56.896,23.371l-14.525-14.512c-2.941-2.944-7.705-2.944-10.646,0l-25.289,25.292
          c-1.412,1.411-2.208,3.33-2.208,5.324c0,1.995,0.796,3.913,2.208,5.325l14.223,14.213c-12.142,17.082-20.458,36.624-24.345,57.204
          H10.195c-4.16,0-7.533,3.366-7.533,7.53v35.761c0,4.162,3.374,7.533,7.533,7.533h18.35c3.453,20.739,11.458,40.587,23.423,58.074
          l-13.335,13.33c-2.944,2.946-2.944,7.71,0,10.65l25.291,25.297c1.407,1.41,3.331,2.209,5.325,2.209
          c1.994,0,3.911-0.799,5.321-2.209l12.917-12.91c17.628,12.618,37.855,21.151,59.172,24.92v18.046c0,4.159,3.373,7.531,7.529,7.531
          h35.768c4.156,0,7.529-3.372,7.529-7.531v-18.046c21.142-3.736,41.24-12.176,58.772-24.652l13.308,13.311
          c2.824,2.821,7.825,2.821,10.648,0l25.294-25.293c2.941-2.945,2.941-7.705,0-10.649l-13.611-13.606
          c12.121-17.595,20.208-37.573,23.688-58.476h20.258c4.152,0,7.528-3.371,7.528-7.536v-35.758
          C343.37,264.822,340.002,261.456,335.842,261.456z M173.023,366.245c-43.837,0-79.378-35.541-79.378-79.376
          c0-43.837,35.536-79.372,79.378-79.372c43.838,0,79.376,35.535,79.376,79.372C252.399,330.704,216.856,366.245,173.023,366.245z
          M203.141,286.869c0,16.634-13.489,30.122-30.118,30.122c-16.632,0-30.119-13.488-30.119-30.122
          c0-16.636,13.486-30.119,30.119-30.119C189.651,256.75,203.141,270.233,203.141,286.869z M450.961,118.12l-10.101-4.35
          c2.714-11.902,2.797-24.216,0.229-36.059l10.752-4.273c2.205-0.882,3.283-3.379,2.415-5.587l-7.559-18.991
          c-0.879-2.213-3.376-3.289-5.591-2.408l-11.083,4.408c-6.467-10.192-15.091-18.87-25.236-25.382l4.63-10.758
          c0.459-1.048,0.472-2.225,0.043-3.294c-0.415-1.057-1.247-1.914-2.292-2.356l-18.775-8.09c-2.176-0.937-4.711,0.074-5.65,2.245
          l-4.768,11.08c-11.489-2.578-23.409-2.696-34.878-0.342l-4.482-11.25c-0.881-2.208-3.385-3.283-5.593-2.41l-18.985,7.56
          c-2.207,0.878-3.285,3.384-2.413,5.587l4.474,11.255c-10.035,6.208-18.668,14.563-25.27,24.424l-10.769-4.637
          c-2.188-0.941-4.715,0.065-5.658,2.246l-8.087,18.772c-0.449,1.053-0.472,2.229-0.042,3.294c0.419,1.059,1.246,1.91,2.296,2.358
          l10.55,4.55c-2.835,11.628-3.124,23.765-0.842,35.514l-10.08,4.013c-2.205,0.878-3.283,3.379-2.405,5.587l7.55,18.988
          c0.888,2.213,3.381,3.292,5.591,2.411l9.752-3.877c6.211,10.288,14.653,19.136,24.7,25.891l-4.263,9.896
          c-0.934,2.187,0.065,4.72,2.25,5.661l18.771,8.085c1.05,0.449,2.232,0.471,3.286,0.044c1.057-0.418,1.91-1.247,2.358-2.29
          l4.128-9.581c12.027,2.974,24.568,3.231,36.685,0.733l3.807,9.576c0.881,2.213,3.381,3.289,5.593,2.41l18.989-7.554
          c2.209-0.876,3.283-3.385,2.411-5.587l-3.812-9.582c10.435-6.448,19.321-15.173,26.002-25.505l9.876,4.257
          c2.096,0.897,4.754-0.15,5.654-2.246l8.087-18.771C454.153,121.587,453.141,119.062,450.961,118.12z M376.782,136.487
          c-23.271,9.256-49.647-2.101-58.9-25.379c-9.257-23.275,2.101-49.645,25.376-58.911c23.271-9.25,49.646,2.106,58.915,25.376
          C411.426,100.851,400.061,127.226,376.782,136.487z M376.017,87.978c3.515,8.832-0.799,18.84-9.629,22.353
          c-8.834,3.513-18.836-0.801-22.36-9.633c-3.502-8.832,0.804-18.837,9.633-22.351C362.489,74.834,372.504,79.149,376.017,87.978z"
        />
      </g>
      <g v-if="name === 'relatorios'">
        <path d="M389.513,87.422c0-12.012-4.688-23.32-13.184-31.816l-42.422-42.422C325.529,4.805,313.636,0,301.8,0h-2.578v90h90.292L389.513,87.422L389.513,87.422z"/>
        <path d="M273.937,309.537c2.871-8.716,7.881-16.831,14.414-23.408l101.562-101.153V120h-105.4c-8.291,0-14.513-6.709-14.513-15V0
          H45C20.186,0,0,20.186,0,45v422c0,24.814,20.186,45,45,45h299.513c24.814,0,45.4-20.186,45.4-45V355.049l-16.484,16.084
          c-6.679,6.621-14.501,11.44-23.32,14.385l-47.695,15.923l-7.266,0.396c-12.012,0-23.379-5.845-30.439-15.63
          c-7.002-9.741-8.906-22.368-5.098-33.779L273.937,309.537z M75,270h149.513c8.291,0,15,6.709,15,15c0,8.291-6.709,15-15,15H75
          c-8.291,0-15-6.709-15-15C60,276.709,66.709,270,75,270z M60,225c0-8.291,6.709-15,15-15h149.513c8.291,0,15,6.709,15,15
          s-6.709,15-15,15H75C66.709,240,60,233.291,60,225z M60,345c0-8.291,6.709-15,15-15h149.513c8.291,0,15,6.709,15,15
          c0,8.291-6.709,15-15,15H75C66.709,360,60,353.291,60,345z M284.513,420c8.291,0,15,6.709,15,15c0,8.291-6.708,15-15,15h-90
          c-8.291,0-15-6.709-15-15c0-8.291,6.709-15,15-15H284.513z M75,180c-8.291,0-15-6.709-15-15s6.709-15,15-15h209.513
          c8.291,0,15,6.709,15,15s-6.709,15-15,15H75z"
        />
        <path d="M301.111,322.808l-13.05,39.151c-1.956,5.865,3.625,11.444,9.49,9.485l39.128-13.068L301.111,322.808z"/>
        <path d="M417.609,199.307l-98.789,98.789l42.605,42.605c22.328-22.332,65.773-65.783,98.784-98.794L417.609,199.307z"/>
        <path d="M503.185,156.284c-5.273-5.303-13.037-8.335-21.27-8.335c-8.233,0-15.996,3.032-21.299,8.35l-21.797,21.797l42.598,42.598c11.933-11.934,20.181-20.182,21.799-21.799C514.933,187.16,514.932,168.046,503.185,156.284z"/>
        <path d="M503.215,198.896c0.001,0,0.001-0.001,0.002-0.002c0.038-0.038,0.055-0.055,0.086-0.086C503.272,198.84,503.255,198.857,503.215,198.896z"/>
        <path d="M503.303,198.808c0.048-0.048,0.104-0.104,0.133-0.133C503.406,198.705,503.351,198.76,503.303,198.808z"/>
        <path d="M503.436,198.675C503.533,198.578,503.535,198.576,503.436,198.675L503.436,198.675z"/>
      </g>
      <g v-if="name === 'ger_usuarios'">
        <path d="M147.128,91.076c0-37.95,30.766-68.716,68.721-68.716c37.95,0,68.719,30.766,68.719,68.716s-30.769,68.715-68.719,68.715
          C177.894,159.792,147.128,129.026,147.128,91.076z M248.873,206.607c0.689-14.963,5.84-28.812,14.127-40.261
          c-5.816-1.218-11.827-1.865-17.995-1.865h-58.304c-6.15,0-12.153,0.642-17.939,1.845c8.819,12.232,14.094,27.171,14.18,43.343
          c10.72-5.896,23.02-9.253,36.085-9.253C229.625,200.416,239.714,202.624,248.873,206.607z M260.505,212.775
          c19.96,12.517,33.957,33.688,36.517,58.274c8.133,3.801,17.171,5.994,26.746,5.994c34.968,0,63.311-28.346,63.311-63.313
          c0-34.971-28.343-63.311-63.311-63.311C289.12,150.42,261.031,178.257,260.505,212.775z M219.026,342.411
          c34.962,0,63.307-28.354,63.307-63.311c0-34.962-28.345-63.311-63.307-63.311c-34.965,0-63.322,28.348-63.322,63.311
          C155.705,314.057,184.061,342.411,219.026,342.411z M245.882,346.72h-53.717c-44.697,0-81.069,36.369-81.069,81.072v65.703
          l0.171,1.029l4.522,1.406c42.658,13.323,79.718,17.779,110.224,17.779c59.571,0,94.114-16.987,96.242-18.074l4.231-2.141h0.449
          v-65.703C326.936,383.089,290.585,346.72,245.882,346.72z M350.638,281.364h-53.314c-0.579,21.332-9.683,40.542-24.081,54.35
          c39.732,11.815,68.802,48.657,68.802,92.178v20.245c52.629-1.938,82.963-16.846,84.961-17.851l4.232-2.152h0.449v-65.715
          C431.693,317.728,395.324,281.364,350.638,281.364z M364.889,149.069c19.961,12.519,33.957,33.691,36.511,58.277
          c8.134,3.801,17.171,5.99,26.746,5.99c34.975,0,63.316-28.342,63.316-63.304c0-34.972-28.342-63.311-63.316-63.311
          C393.503,86.717,365.416,114.56,364.889,149.069z M455.01,217.658h-53.303c-0.579,21.332-9.682,40.542-24.08,54.349
          c39.731,11.811,68.801,48.658,68.801,92.179v20.245c52.624-1.934,82.964-16.84,84.962-17.852l4.226-2.145h0.455v-65.723
          C536.077,254.024,499.708,217.658,455.01,217.658z M107.937,277.044c12.386,0,23.903-3.618,33.67-9.777
          c3.106-20.241,13.958-37.932,29.454-49.975c0.065-1.188,0.174-2.361,0.174-3.561c0-34.971-28.351-63.311-63.298-63.311
          c-34.977,0-63.316,28.339-63.316,63.311C44.621,248.704,72.959,277.044,107.937,277.044z M164.795,335.714
          c-14.331-13.742-23.404-32.847-24.072-54.055c-1.971-0.147-3.928-0.295-5.943-0.295H81.069C36.366,281.364,0,317.728,0,362.425
          v65.709l0.166,1.023l4.528,1.412c34.214,10.699,64.761,15.616,91.292,17.153v-19.837
          C95.991,384.371,125.054,347.523,164.795,335.714z"
        />
      </g>
      <g v-if="name === 'estoque'">
        <polygon points="83.115,172.911 376.304,172.911 376.304,459.419 458.777,459.419 458.777,106.956
          223.178,0 0.642,106.956 0.642,459.419 83.115,459.419"
        />
        <rect x="171.155" y="403.543" width="52.023" height="44.878"/>
        <rect x="235.005" y="403.543" width="52.023" height="44.878"/>
        <rect x="107.313" y="403.543" width="52.023" height="44.878"/>
        <rect x="171.155" y="341.985" width="52.023" height="44.878"/>
        <rect x="107.313" y="341.985" width="52.023" height="44.878"/>
        <rect x="235.005" y="341.985" width="52.023" height="44.878"/>
        <rect x="171.155" y="280.436" width="52.023" height="44.878"/>
        <rect x="107.313" y="280.436" width="52.023" height="44.878"/>
        <rect x="298.847" y="403.543" width="52.023" height="44.878"/>
      </g>
      <g v-if="name === 'container'">
        <path d="M0,288.732h381.769c14.812,14.611,28.598,30.48,34.652,50.79c0.092,0.321,0.244,0.589,0.361,0.882
			c0.067,5.049,0.1,10.103,0.16,15.156h-15.594c-6.5,0-11.773,5.266-11.773,11.773s5.273,11.778,11.773,11.778h41.217
			c6.5,0,11.773-5.271,11.773-11.778s-5.273-11.773-11.773-11.773h-5.983c-0.136-22.281-0.497-44.551-0.705-66.82h45.938V100.752H0
			V288.732z M416.253,288.732c0.032,3.158,0.06,6.308,0.092,9.458c-2.452-3.219-4.997-6.393-7.666-9.458H416.253z"/>
		<path d="M75.292,298.92c22.684,0,41.076,18.39,41.076,41.067c0,22.694-18.392,41.076-41.076,41.076
			c-22.68,0-41.072-18.382-41.072-41.076C34.219,317.31,52.611,298.92,75.292,298.92z"/>
		<path d="M163.435,298.92c22.686,0,41.074,18.39,41.074,41.067c0,22.694-18.388,41.076-41.074,41.076
			c-22.68,0-41.072-18.382-41.072-41.076C122.363,317.31,140.754,298.92,163.435,298.92z"/>
      </g>
      <g v-if="name === 'edi'">
        <path d="M238.933,349.748V162.014c0-14.114-11.486-25.6-25.6-25.6H25.6c-14.114,0-25.6,11.486-25.6,25.6v187.733
				c0,14.114,11.486,25.6,25.6,25.6h187.733C227.447,375.348,238.933,363.862,238.933,349.748z M42.667,179.081H128
				c4.71,0,8.533,3.823,8.533,8.533s-3.823,8.533-8.533,8.533H42.667c-4.71,0-8.533-3.823-8.533-8.533
				S37.956,179.081,42.667,179.081z M34.133,290.014c0-4.71,3.823-8.533,8.533-8.533H76.8c4.71,0,8.533,3.823,8.533,8.533
				s-3.823,8.533-8.533,8.533H42.667C37.956,298.548,34.133,294.725,34.133,290.014z M128,332.681H59.733
				c-4.71,0-8.533-3.823-8.533-8.533s3.823-8.533,8.533-8.533H128c4.71,0,8.533,3.823,8.533,8.533S132.71,332.681,128,332.681z
				 M128,264.414H42.667c-4.71,0-8.533-3.823-8.533-8.533c0-4.71,3.823-8.533,8.533-8.533H128c4.71,0,8.533,3.823,8.533,8.533
				C136.533,260.592,132.71,264.414,128,264.414z M162.133,230.281H68.267c-4.71,0-8.533-3.823-8.533-8.533
				c0-4.71,3.823-8.533,8.533-8.533h93.867c4.71,0,8.533,3.823,8.533,8.533C170.667,226.458,166.844,230.281,162.133,230.281z"/>
			<path d="M486.4,136.415H298.667c-14.114,0-25.6,11.486-25.6,25.6v187.733c0,14.114,11.486,25.6,25.6,25.6H486.4
				c14.114,0,25.6-11.486,25.6-25.6V162.014C512,147.9,500.514,136.415,486.4,136.415z M315.733,179.081h85.333
				c4.71,0,8.533,3.823,8.533,8.533s-3.823,8.533-8.533,8.533h-85.333c-4.71,0-8.533-3.823-8.533-8.533
				S311.023,179.081,315.733,179.081z M307.2,290.014c0-4.71,3.823-8.533,8.533-8.533h34.133c4.71,0,8.533,3.823,8.533,8.533
				s-3.823,8.533-8.533,8.533h-34.133C311.023,298.548,307.2,294.725,307.2,290.014z M401.067,332.681H332.8
				c-4.71,0-8.533-3.823-8.533-8.533s3.823-8.533,8.533-8.533h68.267c4.71,0,8.533,3.823,8.533,8.533
				S405.777,332.681,401.067,332.681z M401.067,264.414h-85.333c-4.71,0-8.533-3.823-8.533-8.533c0-4.71,3.823-8.533,8.533-8.533
				h85.333c4.71,0,8.533,3.823,8.533,8.533C409.6,260.592,405.777,264.414,401.067,264.414z M435.2,230.281h-93.867
				c-4.71,0-8.533-3.823-8.533-8.533c0-4.71,3.823-8.533,8.533-8.533H435.2c4.71,0,8.533,3.823,8.533,8.533
				C443.733,226.458,439.91,230.281,435.2,230.281z"/>
			<path d="M123.233,92.873c48.051-23.62,86.519-33.254,132.77-33.254c39.876,0,74.001,7.219,113.434,24.337l-36.83,12.339
				c-4.463,1.493-6.878,6.332-5.376,10.803c1.195,3.567,4.523,5.828,8.09,5.828c0.896,0,1.809-0.145,2.714-0.452l57.207-19.166
				c0.145-0.051,0.247-0.162,0.384-0.222c0.776-0.299,1.485-0.717,2.142-1.237c0.213-0.162,0.427-0.316,0.623-0.503
				c0.674-0.649,1.28-1.374,1.724-2.253c0.017-0.034,0.043-0.06,0.06-0.085c0.008-0.009,0.017-0.017,0.017-0.026
				c0.119-0.239,0.102-0.495,0.196-0.734c0.307-0.794,0.589-1.604,0.648-2.466c0.06-0.904-0.111-1.783-0.333-2.654
				c-0.051-0.205-0.009-0.418-0.077-0.623l-0.017-0.043l-19.157-57.173c-1.493-4.463-6.323-6.895-10.803-5.376
				c-4.463,1.493-6.878,6.332-5.376,10.803l12.851,38.366c-42.487-18.714-79.07-26.53-122.121-26.53
				c-49.075,0-89.719,10.138-140.305,35.004c-4.224,2.082-5.973,7.194-3.891,11.426C113.889,93.206,118.992,94.955,123.233,92.873z"
				/>
			<path d="M388.767,419.127c-48.051,23.62-86.519,33.254-132.77,33.254c-39.876,0-74.001-7.219-113.434-24.337l36.83-12.339
				c4.463-1.493,6.878-6.332,5.376-10.803c-1.493-4.463-6.315-6.878-10.803-5.376l-57.207,19.166
				c-0.017,0.009-0.026,0.017-0.034,0.017c-1.05,0.358-2.022,0.913-2.867,1.655c-0.853,0.742-1.536,1.638-2.031,2.637
				c-0.009,0.008-0.017,0.008-0.017,0.008c-0.119,0.247-0.102,0.495-0.196,0.734c-0.307,0.802-0.589,1.604-0.649,2.475
				c-0.06,0.905,0.111,1.775,0.333,2.645c0.051,0.213,0.009,0.427,0.077,0.631l0.017,0.043l19.157,57.173
				c1.195,3.567,4.523,5.828,8.09,5.828c0.896,0,1.809-0.154,2.714-0.452c4.463-1.493,6.878-6.332,5.376-10.803l-12.851-38.366
				c42.487,18.714,79.07,26.53,122.121,26.53c49.075,0,89.719-10.138,140.305-35.004c4.224-2.082,5.973-7.194,3.891-11.426
				C398.111,418.794,393.008,417.037,388.767,419.127z"/>

      </g>
      <g v-if="name === 'mobile'">
        <circle id="Ellipse_1" data-name="Ellipse 1" cx="19.976" cy="19.976" r="19.976" transform="translate(235.732 365.436)"/>
        <path id="Path_104" data-name="Path 104" d="M233.451,68.248H121.993a11.088,11.088,0,0,0-11.331,10.793V289.73a11.082,11.082,0,0,0,11.331,10.793H233.451a11.088,11.088,0,0,0,11.331-10.793V79.041A11.087,11.087,0,0,0,233.451,68.248Z" transform="translate(77.984 48.096)"/>
        <path id="Path_105" data-name="Path 105" d="M255.7,0C114.483,0,0,114.491,0,255.7S114.483,511.416,255.7,511.416,511.414,396.922,511.414,255.7,396.918,0,255.7,0Zm99.484,381.893a35.373,35.373,0,0,1-35.371,35.371H191.595a35.373,35.373,0,0,1-35.371-35.371V128.4a35.373,35.373,0,0,1,35.371-35.371H319.813A35.373,35.373,0,0,1,355.185,128.4Z" transform="translate(-0.001)"/>
      </g>

      <g v-if="name === 'pt-flag'">
        <path style="fill:#73AF00;" d="M503.172,423.725H8.828c-4.875,0-8.828-3.953-8.828-8.828V97.104c0-4.875,3.953-8.828,8.828-8.828
	h494.345c4.875,0,8.828,3.953,8.828,8.828v317.793C512,419.773,508.047,423.725,503.172,423.725z"/>
<path style="fill:#FFE15A;" d="M251.41,135.209L65.354,248.46c-5.651,3.439-5.651,11.641,0,15.081L251.41,376.793
	c2.819,1.716,6.36,1.716,9.18,0l186.057-113.251c5.651-3.439,5.651-11.641,0-15.081L260.59,135.209
	C257.771,133.493,254.229,133.493,251.41,135.209z"/>
<circle style="fill:#41479B;" cx="256" cy="256.001" r="70.62"/>
<g>
	<path style="fill:#F5F5F5;" d="M195.401,219.874c-3.332,5.578-5.905,11.64-7.605,18.077c39.149-2.946,97.062,8.006,133.922,43.773
		c2.406-6.141,3.994-12.683,4.59-19.522C288.247,230.169,235.628,218.778,195.401,219.874z"/>
	<path style="fill:#F5F5F5;" d="M258.925,280.1l1.88,5.638l5.943,0.046c0.769,0.006,1.088,0.988,0.47,1.445l-4.781,3.531
		l1.793,5.666c0.232,0.734-0.604,1.341-1.229,0.893l-4.835-3.456l-4.835,3.456c-0.626,0.448-1.461-0.159-1.229-0.893l1.793-5.666
		l-4.781-3.531c-0.619-0.457-0.3-1.439,0.469-1.445l5.943-0.046l1.88-5.638C257.649,279.37,258.681,279.37,258.925,280.1z"/>
	<path style="fill:#F5F5F5;" d="M282.024,294.685l0.809,2.426l2.558,0.02c0.331,0.002,0.469,0.425,0.202,0.622l-2.058,1.519
		l0.771,2.439c0.1,0.316-0.259,0.577-0.529,0.384l-2.081-1.487l-2.081,1.487c-0.269,0.193-0.629-0.068-0.529-0.384l0.771-2.439
		l-2.058-1.519c-0.266-0.196-0.129-0.619,0.202-0.622l2.558-0.02l0.809-2.426C281.474,294.37,281.919,294.37,282.024,294.685z"/>
	<path style="fill:#F5F5F5;" d="M248.938,269.39l0.809,2.426l2.558,0.02c0.331,0.002,0.469,0.425,0.202,0.622l-2.058,1.519
		l0.771,2.439c0.1,0.316-0.259,0.577-0.529,0.384l-2.081-1.487l-2.081,1.487c-0.269,0.193-0.629-0.068-0.529-0.384l0.771-2.439
		l-2.058-1.519c-0.266-0.196-0.129-0.619,0.202-0.622l2.558-0.02l0.809-2.426C248.388,269.076,248.833,269.076,248.938,269.39z"/>
	<path style="fill:#F5F5F5;" d="M204.13,266.448l0.809,2.426l2.558,0.02c0.331,0.002,0.469,0.425,0.202,0.622l-2.058,1.519
		l0.771,2.439c0.1,0.316-0.259,0.577-0.529,0.384l-2.081-1.487l-2.081,1.487c-0.269,0.193-0.629-0.068-0.529-0.384l0.771-2.439
		l-2.058-1.519c-0.266-0.196-0.129-0.619,0.202-0.622l2.558-0.02l0.809-2.426C203.581,266.134,204.025,266.134,204.13,266.448z"/>
	<path style="fill:#F5F5F5;" d="M241.614,293.847l0.809,2.426l2.558,0.02c0.331,0.002,0.469,0.425,0.202,0.622l-2.058,1.519
		l0.771,2.439c0.1,0.316-0.259,0.577-0.529,0.384l-2.081-1.487l-2.081,1.487c-0.269,0.193-0.629-0.068-0.529-0.384l0.771-2.439
		l-2.058-1.519c-0.266-0.196-0.129-0.619,0.202-0.622l2.558-0.02l0.809-2.426C241.065,293.534,241.51,293.534,241.614,293.847z"/>
	<path style="fill:#F5F5F5;" d="M220.99,264.755l0.662,1.984l2.092,0.017c0.27,0.002,0.383,0.348,0.166,0.509l-1.683,1.242
		l0.631,1.994c0.082,0.258-0.212,0.472-0.433,0.314l-1.702-1.216l-1.702,1.216c-0.221,0.158-0.514-0.056-0.433-0.314l0.631-1.994
		l-1.683-1.242c-0.217-0.161-0.106-0.507,0.166-0.509l2.092-0.017l0.662-1.984C220.541,264.498,220.904,264.498,220.99,264.755z"/>
	<path style="fill:#F5F5F5;" d="M283.819,223.794l0.828,2.482l2.616,0.02c0.339,0.002,0.479,0.435,0.206,0.636l-2.104,1.554
		l0.789,2.495c0.103,0.323-0.266,0.59-0.541,0.393l-2.129-1.522l-2.129,1.522c-0.276,0.198-0.643-0.071-0.541-0.393l0.789-2.495
		l-2.104-1.554c-0.273-0.201-0.132-0.633,0.206-0.636l2.616-0.02l0.828-2.482C283.257,223.472,283.712,223.472,283.819,223.794z"/>
	<path style="fill:#F5F5F5;" d="M207.012,252.617l0.662,1.984l2.092,0.017c0.27,0.002,0.383,0.348,0.166,0.509l-1.683,1.242
		l0.631,1.994c0.082,0.258-0.212,0.472-0.433,0.314l-1.702-1.216l-1.702,1.216c-0.221,0.158-0.514-0.056-0.433-0.314l0.631-1.994
		l-1.683-1.242c-0.217-0.161-0.106-0.506,0.166-0.509l2.092-0.017l0.662-1.984C206.563,252.36,206.926,252.36,207.012,252.617z"/>
	<path style="fill:#F5F5F5;" d="M217.112,280.581l1.002,3.006l3.168,0.024c0.41,0.003,0.58,0.526,0.25,0.77l-2.549,1.882l0.956,3.02
		c0.124,0.391-0.321,0.715-0.655,0.476l-2.578-1.842l-2.578,1.842c-0.333,0.238-0.779-0.085-0.655-0.476l0.956-3.02l-2.549-1.882
		c-0.33-0.244-0.16-0.767,0.25-0.77l3.168-0.024l1.002-3.006C216.433,280.193,216.983,280.193,217.112,280.581z"/>
	<path style="fill:#F5F5F5;" d="M294.903,295.315l0.63,1.891l1.993,0.015c0.258,0.002,0.365,0.331,0.158,0.484l-1.603,1.184
		l0.601,1.9c0.078,0.246-0.202,0.449-0.413,0.299l-1.621-1.159l-1.622,1.159c-0.21,0.15-0.49-0.053-0.413-0.299l0.601-1.9
		l-1.603-1.184c-0.207-0.153-0.1-0.482,0.158-0.484l1.993-0.015l0.63-1.891C294.475,295.07,294.822,295.07,294.903,295.315z"/>
	<path style="fill:#F5F5F5;" d="M301.877,280.885l0.809,2.426l2.558,0.02c0.331,0.002,0.469,0.425,0.202,0.622l-2.058,1.519
		l0.771,2.439c0.1,0.316-0.259,0.577-0.529,0.384l-2.081-1.487l-2.081,1.487c-0.269,0.193-0.629-0.068-0.529-0.384l0.771-2.439
		l-2.058-1.519c-0.266-0.196-0.129-0.619,0.202-0.622l2.558-0.02l0.809-2.426C301.327,280.57,301.772,280.57,301.877,280.885z"/>
</g>
      </g>

      <g v-if="name === 'es-flag'">
        <path style="fill:#C8414B;" d="M8.828,423.725h494.345c4.875,0,8.828-3.953,8.828-8.828V97.104c0-4.875-3.953-8.828-8.828-8.828
	H8.828C3.953,88.277,0,92.229,0,97.104v317.793C0,419.773,3.953,423.725,8.828,423.725z"/>
<rect y="158.901" style="fill:#FFD250;" width="512" height="194.21"/>
<path style="fill:#C8414B;" d="M216.276,256.001l7.485-33.681c0.69-3.102-1.671-6.044-4.849-6.044h-5.272
	c-3.177,0-5.537,2.942-4.849,6.044L216.276,256.001z"/>
<rect x="207.45" y="238.341" style="fill:#F5F5F5;" width="17.655" height="75.03"/>
<rect x="203.03" y="229.521" style="fill:#FAB446;" width="26.483" height="8.828"/>
<g>
	<rect x="185.38" y="256.001" style="fill:#C8414B;" width="44.14" height="8.828"/>
	<polygon style="fill:#C8414B;" points="229.517,291.311 203.034,282.484 203.034,273.656 229.517,282.484 	"/>
	<path style="fill:#C8414B;" d="M83.862,256.001l7.485-33.681c0.69-3.102-1.671-6.044-4.849-6.044h-5.272
		c-3.177,0-5.537,2.942-4.849,6.044L83.862,256.001z"/>
</g>
<path style="fill:#F5F5F5;" d="M114.759,229.518c-4.875,0-8.828,3.953-8.828,8.828v57.379c0,10.725,10.01,30.897,44.138,30.897
	s44.138-20.171,44.138-30.897v-57.379c0-4.875-3.953-8.828-8.828-8.828H114.759z"/>
<g>
	<path style="fill:#C8414B;" d="M150.069,273.656h-44.138v-35.31c0-4.875,3.953-8.828,8.828-8.828h35.31V273.656z"/>
	<path style="fill:#C8414B;" d="M150.069,273.656h44.138v22.069c0,12.189-9.88,22.069-22.069,22.069l0,0
		c-12.189,0-22.069-9.88-22.069-22.069V273.656z"/>
</g>
<path style="fill:#FAB446;" d="M105.931,273.656h44.138v22.069c0,12.189-9.88,22.069-22.069,22.069l0,0
	c-12.189,0-22.069-9.88-22.069-22.069V273.656z"/>
<g>
	<path style="fill:#C8414B;" d="M141.241,313.281v-39.625h-8.828v43.693C135.697,316.683,138.664,315.229,141.241,313.281z"/>
	<path style="fill:#C8414B;" d="M123.586,317.349v-43.693h-8.828v39.625C117.336,315.229,120.303,316.683,123.586,317.349z"/>
</g>
<rect x="114.76" y="256.001" style="fill:#FFB441;" width="26.483" height="8.828"/>
<g>
	<rect x="114.76" y="238.341" style="fill:#FAB446;" width="26.483" height="8.828"/>
	<rect x="119.17" y="243.591" style="fill:#FAB446;" width="17.655" height="15.992"/>
</g>
<rect x="75.03" y="238.341" style="fill:#F5F5F5;" width="17.655" height="75.03"/>
<g>
	<rect x="70.62" y="308.971" style="fill:#FAB446;" width="26.483" height="8.828"/>
	<rect x="70.62" y="229.521" style="fill:#FAB446;" width="26.483" height="8.828"/>
</g>
<rect x="66.21" y="317.791" style="fill:#5064AA;" width="35.31" height="8.828"/>
<rect x="207.45" y="308.971" style="fill:#FAB446;" width="26.483" height="8.828"/>
<rect x="198.62" y="317.791" style="fill:#5064AA;" width="35.31" height="8.828"/>
<rect x="123.59" y="220.691" style="fill:#FAB446;" width="52.966" height="8.828"/>
<rect x="145.66" y="194.211" style="fill:#FFB441;" width="8.828" height="26.483"/>
<g>
	<path style="fill:#F5F5F5;" d="M141.241,207.449c-7.302,0-13.241-5.94-13.241-13.241c0-7.302,5.94-13.241,13.241-13.241
		c7.302,0,13.241,5.94,13.241,13.241C154.483,201.509,148.543,207.449,141.241,207.449z M141.241,189.794
		c-2.435,0-4.414,1.978-4.414,4.414c0,2.435,1.978,4.414,4.414,4.414s4.414-1.978,4.414-4.414
		C145.655,191.773,143.677,189.794,141.241,189.794z"/>
	<path style="fill:#F5F5F5;" d="M158.897,207.449c-7.302,0-13.241-5.94-13.241-13.241c0-7.302,5.94-13.241,13.241-13.241
		c7.302,0,13.241,5.94,13.241,13.241S166.198,207.449,158.897,207.449z M158.897,189.794c-2.435,0-4.414,1.978-4.414,4.414
		c0,2.435,1.978,4.414,4.414,4.414c2.435,0,4.414-1.978,4.414-4.414C163.31,191.773,161.332,189.794,158.897,189.794z"/>
	<path style="fill:#F5F5F5;" d="M176.552,216.277c-7.302,0-13.241-5.94-13.241-13.241c0-7.302,5.94-13.241,13.241-13.241
		c7.302,0,13.241,5.94,13.241,13.241S183.853,216.277,176.552,216.277z M176.552,198.622c-2.435,0-4.414,1.978-4.414,4.414
		c0,2.435,1.978,4.414,4.414,4.414c2.435,0,4.414-1.978,4.414-4.414S178.987,198.622,176.552,198.622z"/>
	<path style="fill:#F5F5F5;" d="M123.586,216.277c-7.302,0-13.241-5.94-13.241-13.241c0-7.302,5.94-13.241,13.241-13.241
		c7.302,0,13.241,5.94,13.241,13.241C136.828,210.337,130.888,216.277,123.586,216.277z M123.586,198.622
		c-2.435,0-4.414,1.978-4.414,4.414c0,2.435,1.978,4.414,4.414,4.414s4.414-1.979,4.414-4.415
		C128,200.6,126.022,198.622,123.586,198.622z"/>
</g>
<path style="fill:#FAB446;" d="M176.552,291.311v4.414c0,2.434-1.98,4.414-4.414,4.414s-4.414-1.98-4.414-4.414v-4.414H176.552
	 M185.379,282.484h-26.483v13.241c0,7.302,5.94,13.241,13.241,13.241c7.302,0,13.241-5.94,13.241-13.241v-13.241H185.379z"/>
<path style="fill:#FFA0D2;" d="M172.138,264.829L172.138,264.829c-4.875,0-8.828-3.953-8.828-8.828v-8.828
	c0-4.875,3.953-8.828,8.828-8.828l0,0c4.875,0,8.828,3.953,8.828,8.828v8.828C180.966,260.876,177.013,264.829,172.138,264.829z"/>
<circle style="fill:#5064AA;" cx="150.07" cy="273.651" r="13.241"/>
<rect x="145.66" y="176.551" style="fill:#FAB446;" width="8.828" height="26.483"/>
<path style="fill:#C8414B;" d="M123.586,220.691l-8.828-8.828l5.171-5.171c7.993-7.993,18.835-12.484,30.14-12.484l0,0
	c11.305,0,22.146,4.491,30.14,12.484l5.171,5.171l-8.828,8.828H123.586z"/>
<g>
	<circle style="fill:#FFD250;" cx="150.07" cy="211.861" r="4.414"/>
	<circle style="fill:#FFD250;" cx="132.41" cy="211.861" r="4.414"/>
	<circle style="fill:#FFD250;" cx="167.72" cy="211.861" r="4.414"/>
</g>
<g>
	<rect x="70.62" y="256.001" style="fill:#C8414B;" width="44.14" height="8.828"/>
	<polygon style="fill:#C8414B;" points="70.621,291.311 97.103,282.484 97.103,273.656 70.621,282.484 	"/>
</g>
      </g>
    </g>
  </svg>
</template>

<script>
  export default {
    props: {
      name: {
        type: String,
        default: 'box'
      },
      size: {
        type: [Number, String],
        default: 18
      },
      color: {
        type: String,
        default: '#000'
      }
    }
  }
</script>
