<template>
  <v-flex class="container">
    <img src="@/assets/recovery_password.png">
    <h2>{{ $t('passwordRecovered') }}</h2>
    <p>{{ $t('weShipPassword') }}</p>
    <v-btn
      flat
      dark
      @click="login"
    >
      {{ $t('loginNow') }}
    </v-btn>
  </v-flex>
</template>

<script>
export default {
  methods: {
    login(){
      this.$router.push({ name: 'login_form' })
    }
  }
}
</script>

<style lang="scss" scoped>
  .container{
    display: flex;
    justify-content: center;
    align-items: center;

    h2{
      font-size: 2em;
      font-weight: 700;
      margin-top: 30px;
      color: #333;
    }

    p{
      font-weight: 500;
      font-size: 20px;
      color: #666;
      margin-top: 10px;
      width: 60%;
      text-align: center;
    }

    button{
      background: #2F80ED;
    }
  }
</style>
