<template>
  <v-dialog
    persistent
    v-model="statusProfile"
    max-width="800"
  >
    <v-card class="card-custom">
      <v-layout wrap>
        <v-flex sm4 class="card-custom__profile">
          <v-avatar size="150">
            <img :src="/(.*\w+\.\w{3})|(data:image\/\w{3};)/g.exec(user.picture) ? user.picture : require('@/assets/user.png')" @error="replaceByDefault">
          </v-avatar>

          <v-btn
            flat
            dark
            @click="$refs.photo.click()"
          >
            {{ $t('changePicture') }}
          </v-btn>
          <input
            type="file"
            ref="photo"
            name="photo"
            class="card-custom__profile__inputPhoto"
            @change="onFileChange($event, $event.target.name, $event.target.files)"
          >
        </v-flex>

        <v-flex sm8 class="card-custom__form">
          <v-form>
            <v-text-field
              ref="name"
              :label="$t('fullName')"
              :value="user.name"
              v-model="username"
            />

            <v-text-field
              ref="email"
              :label="$t('email')"
              :value="user.email"
              disabled
            />

            <v-text-field
              ref="password"
              :label="$t('password')"
              placeholder="**********"
              required
              type="password"
              v-model="password.first"
            />

            <v-text-field
              ref="c-passowrd"
              :label="$t('cPassword')"
              placeholder="**********"
              required
              type="password"
              v-model="password.second"
            />

            <!-- <v-select
              :label="$t('country')"
              :items="country"
              value="pt"
            /> -->
          </v-form>

          <v-card-actions class="card-custom__actions">
            <v-spacer></v-spacer>
            <v-btn color="#54a0ff" dark @click="save" class="card-custom__actions__button">{{ $t('record') }}</v-btn>
            <v-btn @click="close" class="card-custom__actions__button" flat>{{ $t('cancel') }}</v-btn>
          </v-card-actions>
        </v-flex>
      </v-layout>
    </v-card>

    <v-snackbar
      v-model="snackbarStatus"
    >
      {{ snackbar.msg }}
    </v-snackbar>
  </v-dialog>
</template>

<script>
import axios from 'axios'
import { mapGetters } from 'vuex'
import { getStorage } from '@/utils/localStorage'

export default {
  data(){
    return {
      country: [
        { text: 'Brasil', value: 'pt' },
        { text: 'Argentina', value: 'es' }
      ],
      maxSize: 1024,
      snackbarStatus: false,
      snackbar: {
        status: false,
        msg: ''
      },
      imagem: '',
      username: '',
      email: '',
      password: {
        first: '',
        second: ''
      }
    }
  },
  computed: {
    ...mapGetters({ user: 'Account/user' }),
    statusProfile(){
      return this.$store.state.Users.profile
    }
  },
  watch: {
    user(){
      this.username = this.user.name
    }
  },
  methods: {
    async onFileChange(e, fieldName, file){
      const { maxSize } = this
      const imageFile = file[0]

      this.picture = URL.createObjectURL(imageFile)
      this.imagem =  this.$refs.photo.files[0]

      this.upload()
    },
    replaceByDefault(e) {
      e.target.src = require('@/assets/user.png')
    },
    close(){
      this.$store.dispatch('Users/changeStatusModal', { name: 'profile' })
    },
    async upload(){
      this.$store.commit('setLoading', true)
      try{
        let formData = new FormData()
        formData.append('picture', this.imagem)

        const { data } = await axios.post(`/user/${this.user.id}/picture`, formData, {
          headers: { Authorization: `Bearer ${getStorage('token')}`}
        })

        await this.$store.dispatch('Account/getUser')
        this.$store.commit('setLoading', false)
        this.snackbar.msg = data.message
        this.snackbarStatus = true
      }catch(e){
        this.$store.commit('setLoading', false)
      }
    },
    async save(){
      if(this.password.first === this.password.second){
        try{
          await this.$store.dispatch('Account/changeAccount', { name: this.username, email: this.user.email, password: this.password.first })
          this.snackbarStatus = true
          this.snackbar.msg = 'Informações alteradas com sucesso!'
        }catch(e){
          this.snackbarStatus = true
          this.snackbar.msg = e.split('\"')[1]
        }
      }else{
        this.snackbarStatus = true
        this.snackbar.msg = 'As senhas não coincidem'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .card-custom{
    &__profile{
      background: #54a0ff;
      justify-content: center;
      align-items: center;
      display: flex;
      padding: 50px 0;
      flex-direction: column;

      &__inputPhoto{
        display: none;
      }
    }

    &__form{
      padding: 50px 50px 25px 50px;
    }

    &__actions{
      margin-top: 20px;

      &__button{
        box-shadow: none !important;
        font-weight: 600;
      }
    }
  }
</style>
