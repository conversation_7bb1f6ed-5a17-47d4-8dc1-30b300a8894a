<template>
  <v-form
    ref="form"
    class="form-custom"
    v-model="valid"
    lazy-validation
    @submit.prevent="login"
    id="check-login-form"
    style="order: 2"
  >
    <h2>{{ $t('login') }}</h2>

    <v-text-field
      :label="$t('user')"
      id="email"
      v-model="user"
      :rules="[rules.required]"
    />

    <v-text-field
      :append-icon="passwordShow ? 'visibility' : 'visibility_off'"
      :label="$t('password')"
      id="password"
      :type="passwordShow ? 'text' : 'password'"
      :rules="[rules.required]"
      @click:append="passwordShow = !passwordShow"
      v-model="password"
    />

    <div class="form-custom__actions">
      <router-link :to="{ name: 'forgot_password' }" id="forgot-password">
        {{ $t('forgotPassword') }}
      </router-link>
      <v-btn
        :class="valid ? 'button' : 'disabled'"
        flat
        dark
        type="submit"
        :disabled="!valid"
        id="login"
      >
        {{ $t('signin') }}
      </v-btn>
    </div>
    <v-snackbar
      v-model="snackbar"
    >
      {{ message }}
      <v-btn
        flat
        @click="snackbar = false"
      >
        Fechar
      </v-btn>
    </v-snackbar>
    <img src="/asp/images/LogoEmpresa.png" onerror="this.style.display='none'"  style="order: 1; margin-bottom: 3vh; display: none" id="logoEmpresa"/>
  </v-form>
</template>

<script>

import rules from '@/utils/rules'
import { mapGetters } from 'vuex'

export default {
  data(){
    return{
      user: '',
      password: '',
      passwordShow: '',
      valid: true,
      rules,
      message: '',
      snackbar: false,
      mouseOverUserInput: false,
      inputInterval: null
    }
  },
  methods: {
    async login(){
      try{
        const res = await this.$store.dispatch('Account/login', { usuario: this.user, senha: this.password })

        this.$router.push({ name: 'dashboard' })
      }catch(e){
        this.message = e.split('\"')[1]
        this.snackbar = true
      }
    }
  },
  mounted (){
    if (document.querySelectorAll('#logoEmpresa').length > 1)
      document.querySelectorAll('#logoEmpresa')[1].remove()

    let logoEmpresa = document.getElementById('logoEmpresa')

    if (document.querySelector('.contentLogin>#logoEmpresa') === null) {
      document.querySelector('.contentLogin').appendChild(logoEmpresa)
    }
    logoEmpresa.style.display = 'block';

    let userInput = document.getElementById('email')
    userInput.focus()

    userInput.addEventListener('mouseover', ()=> {
       this.mouseOverUserInput = true;
    })
    document.getElementById('password').addEventListener('mouseover', ()=> {
       this.mouseOverUserInput = true;
    })

    userInput.addEventListener("input", function () {
      if (this.value.length > 0) clearInterval(this.inputInterval);
    });

    this.inputInterval = setInterval(function() {
    
      if(this.mouseOverUserInput == false && document.querySelector('input:-internal-autofill-selected') !== null && document.getElementById('email').value == '') 
      {
        const email = document.getElementById('email');
        const password = document.getElementById('password');

        let event = document.createEvent('TextEvent');

        if (event.initTextEvent) {
            event.initTextEvent('textInput', true, true, window, '@@@@@');
            email.dispatchEvent(event);
            password.dispatchEvent(event);
            email.value = email.value.replace('@@@@@','');
            password.value = password.value.replace('@@@@@','');
        }

        email.blur();
        email.focus();
      }
    }.bind(this),10);
  },
  beforeDestroy(){
    document.getElementById('logoEmpresa').style.display = 'none'
    clearInterval(this.inputInterval);
  }
}
</script>

<style lang="scss" scoped>
  @import 'essencials';

  .form-custom{
    width: 60%;

    @include breakpoint-mobile {
      width: 90%;
    }

    h2{
      margin-bottom: 30px;
      color: #444;
    }

    &__actions{
      display: flex;
      justify-content: space-between;
      align-items: center;

      a{
        color: #666;
        text-decoration: none;
        font-weight: 600;
        transition: .5s;
      }

      a:hover{
        color: #2F80ED;
      }

      .button{
        background: #2F80ED;
        font-weight: 600;
      }

      .disabled{
        background: rgba(0,0,0,0.12);
        color: rgba(0,0,0,0.26) !important;
      }
    }
  }
</style>

