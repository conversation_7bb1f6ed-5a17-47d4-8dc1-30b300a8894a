import Vue from 'vue'
import './plugins/vuetify'
import App from './App.vue'
import router from './router/'
import store from './store/'
import keycloak from './keycloak'
import VueI18n from 'vue-i18n'
import pt from '@/locales/pt-BR'
import es from '@/locales/es-ES'
// import { createProvider } from './vue-apollo'
import { DEFAULT_API_URLS } from '@/utils/enumql'
import { getStorage } from '@/utils/localStorage'

import { InMemoryCache } from 'apollo-cache-inmemory'
import ApolloClient from 'apollo-boost'
import VueApollo from 'vue-apollo'

Vue.use(VueApollo)
Vue.use(VueI18n)

const cache = new InMemoryCache()
const withBaseURLContext = () => process.env.NODE_ENV
  ? DEFAULT_API_URLS[process.env.NODE_ENV.toUpperCase()]
  : DEFAULT_API_URLS.development

export const defaultClient = new ApolloClient({
  defaultOptions: {
    watchQuery: { fetchPolicy: 'no-cache', errorPolicy: 'ignore' },
    query: { fechPolicy: 'no-cache', errorPolicy: 'all' }
  },
  // cache,
  uri: withBaseURLContext()
})

const apolloProvider = new VueApollo({ defaultClient })

const messages = {
  pt,
  es
}

const i18n = new VueI18n({
  locale: 'pt',
  fallbackLocale: 'pt',
  messages
})

Vue.config.productionTip = false

keycloak.init({ onLoad: 'login-required' }).then(authenticated => {
  if (authenticated) {
    // Aqui você pode salvar tokens/cookies se necessário
    new Vue({
      router,
      store,
      provide: apolloProvider.provide(),
      i18n,
      render: h => h(App)
    }).$mount('#app')
  } else {
    window.location.reload()
  }
})