/* 
// Rafael <PERSON>

Breakpoint

This breakpoints are maded to handle with resolutions and mobile 
How to use:
.some-css-block {
  margin-top: 20px;

  @include breakpoint-mobile {
    margin-top: 0;
  }
}
*/

//mobile
@mixin breakpoint-xs { 
  @media (min-width: $breakpoints-xs) {
    @content;
  }
}

@mixin breakpoint-sm { 
  @media (max-width: $breakpoints-sm) {
    @content;
  }
}

// tablet or mobile
@mixin breakpoint-mobile { 
  @media (max-width: $breakpoints-md) {
    @content;
  }
}

@mixin breakpoint-tablet { 
  @media (max-width: $breakpoints-md) {
    @content;
  }
}

@mixin breakpoint-md { 
  @media (max-width: $breakpoints-md) {
    @content;
  }
}

//desktop
@mixin breakpoint-desktop { 
  @media (max-width: $breakpoints-lg) {
    @content;
  }
}

@mixin breakpoint-lg { 
  @media (max-width: $breakpoints-lg) {
    @content;
  }
}

@mixin breakpoint-xl { 
  @media (max-width: $breakpoints-xl) {
    @content;
  }
}

@mixin breakpoint-xxl { 
  @media (max-width: $breakpoints-xxl) {
    @content;
  }
}

//custom sizes
@mixin breakpoint-custom( $size ) {
  @media (max-width: $size) {
    @content;
  }
}