FROM node:11.9-alpine AS build

RUN apk add --update 
RUN apk add python make g++

RUN mkdir -p /usr/app 
WORKDIR /usr/app

COPY . /usr/app/

RUN npm clean-install --loglevel verbose
# RUN npm install 
RUN npm run build

RUN ls -la

 
# production environment

FROM nginx:1.21.3-alpine

COPY --from=build /usr/app/dist /usr/share/nginx/html

COPY nginx_config/nginx.conf /etc/nginx/nginx.conf
COPY nginx_config/default.conf /etc/nginx/conf.d/default.conf

RUN apk update \
	&& apk upgrade \
	&& apk add bash \
	&& apk add curl
	
# make all files belong to the nginx user
RUN chown nginx:nginx /usr/share/nginx/html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
 
 
 
 
 
 
 
