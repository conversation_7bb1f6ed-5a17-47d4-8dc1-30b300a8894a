#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 16.x (LTS version that should be compatible with Vue 2.x and older Jest)
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm versions
node --version
npm --version

# Navigate to workspace directory
cd /mnt/persist/workspace

# Clean any existing node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Install dependencies using npm install
npm install --loglevel verbose

# Update Jest configuration to handle ES6 modules properly
cat > jest.config.js << 'EOF'
module.exports = {
  moduleFileExtensions: [
    'js',
    'jsx',
    'json',
    'vue'
  ],
  transform: {
    '^.+\\.vue$': 'vue-jest',
    '.+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$': 'jest-transform-stub',
    '^.+\\.jsx?$': 'babel-jest'
  },
  transformIgnorePatterns: [
    '/node_modules/(?!(vue-test-utils|@vue/test-utils)/)'
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  snapshotSerializers: [
    'jest-serializer-vue'
  ],
  testMatch: [
    '**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)'
  ],
  testURL: 'http://localhost/',
  testEnvironment: 'jsdom',
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ]
}
EOF

# Update babel config to ensure proper ES6 module transformation
cat > babel.config.js << 'EOF'
module.exports = {
  presets: [
    ['@vue/app', {
      useBuiltIns: 'entry'
    }]
  ],
  env: {
    test: {
      presets: [
        ['@vue/app', {
          targets: { node: 'current' }
        }]
      ]
    }
  }
}
EOF

# Add npm global bin to PATH in user profile
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> $HOME/.profile

# Source the profile to make PATH available immediately
source $HOME/.profile

echo "Setup completed successfully!"
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "Dependencies installed successfully"
echo "Jest configuration updated for ES6 module compatibility"