node {
    env.NODEJS_HOME = "${tool 'node'}"
    env.PATH = "${env.NODEJS_HOME}/bin:${env.PATH}"
 
    def app
    def workspace = env.WORKSPACE
    def version
    def version_qa
    def app_name = "apresentacao"
    def image_name = "ecargo-menu"
    def image = "signadesenvolvimento/${image_name}"
    def cookbook = "ecargo"
    def recipe = "app"
    def git_url = "ecargo-menu.git"

    stage('Clone repository') {
        git branch: 'master',
            credentialsId: 'git',
            url: "**************:signaconsultoria/${git_url}"

        sh "echo finalizou clone repository"
    }

    stage('Build') {
        app = docker.build("${image}") 
    }

    stage('Tagging') {
        try {
            withCredentials([[$class: 'UsernamePasswordMultiBinding', credentialsId: 'jenkins', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD']]) {
                sh("echo ${env.GIT_USERNAME}")
                sh("git config --global user.email '<EMAIL>'")
                sh("git config --global user.name 'Jenkins'")
                sh("git config credential.username ${env.GIT_USERNAME}")
                sh("git config credential.helper '!f() { echo password=\$GIT_PASSWORD; }; f'")
                // sh('/opt/tools/create-tag')
                
                // version = sh (script: "stepup version | sed -e 's/v//g'", returnStdout: true).trim()
                // echo "next-release: [${version}]"
                echo "next-release: [1.11.9]"
            }   
        } finally {
            sh("git config --unset credential.username")
            sh("git config --unset credential.helper")
        }
        
    }
    
    stage('Promote to QA') {
        version_qa = "1.11.9-qa"
        
        echo "promoting to QA: [${version_qa}]"
        
        docker.withRegistry('https://registry.hub.docker.com', 'docker-registry') {
            app.push("${version_qa}")
        }

        echo "Updating cookbook..."
        try {
            withCredentials([[$class: 'UsernamePasswordMultiBinding', credentialsId: 'jenkins', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD']]) {
                sh("echo ${env.GIT_USERNAME}")
                sh("git config --global user.email '<EMAIL>'")
                sh("git config --global user.name 'Jenkins'")
                sh("git config credential.username ${env.GIT_USERNAME}")
                sh("git config credential.helper '!f() { echo password=\$GIT_PASSWORD; }; f'")

                sh("/opt/tools/promote 'qa' ${cookbook} ${recipe} ${image} ${version} ")
            }   
        } finally {
            sh("git config --unset credential.username")
            sh("git config --unset credential.helper")
        }

    }

    stage('Deploy QA') {
    
        withCredentials([[$class: 'UsernamePasswordMultiBinding', credentialsId: 'docker-registry', usernameVariable: 'REGISTRY_USERNAME', passwordVariable: 'REGISTRY_PASSWORD']]) {

            sh "ssh -o StrictHostKeyChecking=no -l jenkins qa.signa.net.br \'docker login -u ${REGISTRY_USERNAME} -p ${REGISTRY_PASSWORD} && sudo chef-client\'"
        }
        
        sh 'echo "Deployed"'
    }

    stage('Promote to Prod') {
        
        userInput = input(
            id: 'Proceed1', message: 'Promote to production?', parameters: [])

        docker.withRegistry('https://registry.hub.docker.com', 'docker-registry') {
            app.push("${version}")
            app.push("latest")
        }

        echo "Updating cookbook..."
        try {
            withCredentials([[$class: 'UsernamePasswordMultiBinding', credentialsId: 'jenkins', usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD']]) {
                sh("echo ${env.GIT_USERNAME}")
                sh("git config --global user.email '<EMAIL>'")
                sh("git config --global user.name 'Jenkins'")
                sh("git config credential.username ${env.GIT_USERNAME}")
                sh("git config credential.helper '!f() { echo password=\$GIT_PASSWORD; }; f'")

                sh("/opt/tools/promote 'prod' ${cookbook} ${recipe} ${image} ${version} ")
            }   
        } finally {
            sh("git config --unset credential.username")
            sh("git config --unset credential.helper")
        }
    }  

    stage('Deploy Prod') {
    
        withCredentials([[$class: 'UsernamePasswordMultiBinding', credentialsId: 'docker-registry', usernameVariable: 'REGISTRY_USERNAME', passwordVariable: 'REGISTRY_PASSWORD']]) {
            sh "ssh -o StrictHostKeyChecking=no -l root ap.signa.net.br \'docker login -u ${REGISTRY_USERNAME} -p ${REGISTRY_PASSWORD} && sudo chef-client\'"
        }
        
        sh 'echo "Deployed"'
    }
}

