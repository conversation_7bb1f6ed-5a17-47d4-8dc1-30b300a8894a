version: '3.3'
networks:
  backend:
    driver: overlay

  frontend:
    external:
      name: nginx_frontend

services:
  app:
    image: "${IMAGE}:${TAG}"
    networks:
      - frontend
      - backend
    environment:
      - VIRTUAL_HOST=${APP_NAME}.ap.signa.net.br
    deploy:
      mode: replicated
      replicas: 1
      resources:
        limits:
          cpus: "1"
          memory: "1G"
      update_config:
        parallelism: 1
        delay: "10s"
        failure_action: "continue"